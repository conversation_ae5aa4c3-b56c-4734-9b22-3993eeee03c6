"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TraderExecutorService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const backtest_multi_method_service_1 = require("./backtest-multi-method.service");
const configurations_1 = __importDefault(require("../configurations"));
const calculate_equity_ratio_util_1 = require("../util/calculate-equity-ratio.util");
const instrument_service_1 = require("./instrument.service");
const adapter_service_1 = require("./adapter.service");
const to_milliseconds_util_1 = require("../util/to-milliseconds.util");
const get_order_trigger_util_1 = require("../util/get-order-trigger.util");
const trade_result_service_1 = require("./trade-result.service");
const uuid_1 = require("uuid");
const delay_util_1 = require("../util/delay.util");
const method_service_1 = require("./method.service");
const generate_order_value_util_1 = require("../util/generate-order-value.util");
const generate_order_link_id_util_1 = require("../util/generate-order-link-id.util");
const historical_cache_service_1 = require("./historical-cache.service");
const suffle_array_util_1 = require("../util/suffle-array.util");
let TraderExecutorService = class TraderExecutorService {
    logger;
    backtestMultiMethodService;
    instrumentService;
    adapterService;
    tradeResultService;
    methodService;
    historicalCacheService;
    constructor(logger, backtestMultiMethodService, instrumentService, adapterService, tradeResultService, methodService, historicalCacheService) {
        this.logger = logger;
        this.backtestMultiMethodService = backtestMultiMethodService;
        this.instrumentService = instrumentService;
        this.adapterService = adapterService;
        this.tradeResultService = tradeResultService;
        this.methodService = methodService;
        this.historicalCacheService = historicalCacheService;
    }
    async run() {
        try {
            const minProbability = (0, configurations_1.default)('TRADE_MIN_PROBABILITY');
            const methodLimit = (0, configurations_1.default)('METHOD_LIMIT');
            const backtest = await this.backtestMultiMethodService.getBacktestMutilMethodBoth({
                minProbability,
                methodLimit,
                pendingResultOnly: true,
            });
            const results = await this.validatePendingResults(backtest.result);
            const equityRatio = await this.equityRatio(backtest.performance);
            const orderPlans = await this.orderPlan({
                results,
                equityRatio,
            });
            await this.place(orderPlans);
            await this.cancel();
            return;
        }
        catch (error) {
            this.logger.error('Failed to run', (0, log_detail_util_1.logDetail)({
                class: 'TraderExecutorService',
                function: 'run',
                error,
            }));
            await (0, delay_util_1.delay)(5000);
            return;
        }
    }
    async validatePendingResults(results) {
        const pendingResults = [];
        const now = Date.now();
        for (const result of results) {
            const instrument = await this.instrumentService.getInstrument({
                symbol: result.symbol,
            });
            if (!instrument)
                continue;
            const rowTargetLength = (now - Number(instrument[0].listedTime)) /
                (0, to_milliseconds_util_1.toMiliseconds)((0, configurations_1.default)('EXECUTION_INTERVAL'));
            const rowLength = await this.historicalCacheService.countHistorical(result.symbol, (0, configurations_1.default)('EXECUTION_INTERVAL'));
            if (rowLength < rowTargetLength)
                continue;
            const tradeResult = await this.tradeResultService.getClosedResults(100);
            const isClosed = tradeResult.some((item) => item.methodId === result.methodId);
            if (isClosed)
                continue;
            pendingResults.push(result);
        }
        return pendingResults;
    }
    async cancel() {
        const cpuAllocation = (0, configurations_1.default)('CPU_ALLOCATION') ?? (0, configurations_1.default)('TOTAL_CORE');
        try {
            const symbols = await this.instrumentService.getSymbols();
            for (const symbol of (0, suffle_array_util_1.shuffleArray)(symbols)) {
                const activeOrders = await this.adapterService.getActiveOrders({
                    category: 'linear',
                    symbol,
                });
                if (!activeOrders)
                    return;
                for (const activeOrder of activeOrders) {
                    if (activeOrder.price === '0')
                        continue;
                    const tradeResults = await this.tradeResultService.getTradeResultByOrderId(activeOrder.orderId);
                    if (!tradeResults.length) {
                        await this.adapterService.cancelOrder({
                            category: 'linear',
                            symbol: activeOrder.symbol,
                            orderId: activeOrder.orderId,
                        });
                        continue;
                    }
                    for (const tradeResult of tradeResults) {
                        if (tradeResult.status === 'pending' ||
                            tradeResult.status === 'open')
                            continue;
                        await this.adapterService.cancelOrder({
                            category: 'linear',
                            symbol: activeOrder.symbol,
                            orderId: activeOrder.orderId,
                        });
                        await (0, delay_util_1.delay)((1000 / 10) * cpuAllocation);
                    }
                }
            }
            return;
        }
        catch (error) {
            this.logger.error({
                class: 'OrderPlacerService',
                object: 'orderCanceler',
                error: error.message,
            });
            return;
        }
    }
    async place(orderPlans) {
        try {
            const cpuAllocation = (0, configurations_1.default)('CPU_ALLOCATION') ?? (0, configurations_1.default)('TOTAL_CORE');
            for (const orderPlan of orderPlans) {
                const lastPrice = await this.adapterService.fetchBybitHistorical({
                    symbol: orderPlan.symbol,
                    interval: 'D',
                    limit: 1,
                    start: new Date(Date.now() - (0, to_milliseconds_util_1.toMiliseconds)('D')),
                    end: new Date(),
                    sort: 'DESC',
                });
                const tickSize = await this.instrumentService.getTickSize(orderPlan.symbol);
                const updatedOrderPlan = {
                    ...orderPlan,
                    ...(0, get_order_trigger_util_1.getOrderTrigger)({
                        side: orderPlan.side,
                        last: lastPrice[0].close.toString(),
                        entry: orderPlan.price || '0',
                        tickSize: tickSize,
                    }),
                };
                const minOfMaxLeverage = await this.instrumentService.getMinOfMaxLeverage();
                const order = (0, generate_order_value_util_1.generateOrderValue)(updatedOrderPlan);
                await this.adapterService.setLeverage({
                    category: 'linear',
                    symbol: orderPlan.symbol,
                    buyLeverage: minOfMaxLeverage.toFixed(2),
                    sellLeverage: minOfMaxLeverage.toFixed(2),
                });
                await this.adapterService.switchToHedgeMode();
                const activeOrders = await this.adapterService.getActiveOrders({
                    category: 'linear',
                    symbol: orderPlan.symbol,
                });
                if (activeOrders.length) {
                    for (const order of activeOrders) {
                        if (order.price === updatedOrderPlan.price &&
                            order.takeProfit === updatedOrderPlan.takeProfit &&
                            order.stopLoss === updatedOrderPlan.stopLoss &&
                            order.side === updatedOrderPlan.side) {
                            updatedOrderPlan.orderId = order.orderId;
                            await this.adapterService.amendOrder({
                                ...updatedOrderPlan,
                                orderId: order.orderId,
                            });
                            await this.tradeResultService.save(updatedOrderPlan);
                        }
                    }
                }
                else {
                    const response = await this.adapterService.placeOrder(order);
                    if (response && response.result.orderId) {
                        updatedOrderPlan.orderId = response.result.orderId;
                        await this.tradeResultService.save(updatedOrderPlan);
                    }
                }
                await (0, delay_util_1.delay)((1000 / 10) * cpuAllocation);
            }
        }
        catch (error) {
            this.logger.error('Failed to place order', (0, log_detail_util_1.logDetail)({
                class: 'TraderExecutorService',
                function: 'place',
                error,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.place(orderPlans);
        }
    }
    async orderPlan(param) {
        try {
            const orderPlans = [];
            for (const result of param.results) {
                const methodParam = await this.methodService.getParam({
                    methodId: result.methodId,
                });
                if (!methodParam.length)
                    continue;
                const orderLinkId = (0, generate_order_link_id_util_1.generateorderLinkId)(methodParam[0]);
                const side = result.orderType === 'long' ? 'Buy' : 'Sell';
                const positionIdx = result.symbol.includes('USDT')
                    ? result.orderType === 'long'
                        ? 1
                        : 2
                    : 0;
                const qty = await this.calculateQty({
                    equityRatio: param.equityRatio,
                    entryPrice: result.entry,
                    symbol: result.symbol,
                });
                const plan = {
                    id: result.id || (0, uuid_1.v4)(),
                    category: 'linear',
                    symbol: result.symbol,
                    isLeverage: 1,
                    side,
                    orderType: 'Limit',
                    qty: qty.toString(),
                    price: result.entry.toString(),
                    triggerDirection: result.orderType === 'long' ? 1 : 2,
                    triggerPrice: '',
                    triggerBy: 'LastPrice',
                    positionIdx,
                    orderLinkId,
                    takeProfit: result.takeProfit.toString(),
                    stopLoss: result.stopLoss.toString(),
                    tpTriggerBy: 'LastPrice',
                    slTriggerBy: 'LastPrice',
                    tpslMode: 'Partial',
                    tpOrderType: 'Market',
                    slOrderType: 'Market',
                    methodId: result.methodId,
                    date: result.date,
                    interval: result.interval,
                    stopPercent: result.stopPercent,
                    profitPercent: result.profitPercent,
                    expiryDate: result.expiryDate,
                    status: result.status,
                    openDate: result.openDate,
                    closedDate: result.closedDate,
                };
                orderPlans.push(plan);
            }
            return orderPlans;
        }
        catch (error) {
            this.logger.error('Failed to order plan', (0, log_detail_util_1.logDetail)({
                class: 'TraderExecutorService',
                function: 'orderPlan',
                error,
            }));
            return [];
        }
    }
    async equityRatio(performance) {
        const minOfMaxLeverage = await this.instrumentService.getAvgOfMaxLeverage();
        const maxFundingRate = await this.instrumentService.getMaxFundingRate();
        const minFundingInterval = await this.instrumentService.getMinFundingInterval();
        return (0, calculate_equity_ratio_util_1.calculateEquityRatio)({
            minOfMaxLeverage,
            maxOpenPosition: performance.maxOpenPosition,
            maxConsecutiveLoss: performance.maxConsecutiveLoss,
            maxStopPercent: performance.maxStopPercent,
            maxHoldingPeriod: performance.maxHoldingPeriod,
            minFundingInterval,
            maxFundingRate,
        });
    }
    async calculateQty(param) {
        try {
            const walletBalance = await this.adapterService.getWalletBalance();
            const qtyStep = await this.instrumentService.getQtyStep(param.symbol);
            const mulQtyStep = qtyStep.toString().split('.')[1]?.length || 0;
            const maxOrderQty = await this.instrumentService.getMaxOrderQty(param.symbol);
            const minOrderQty = await this.instrumentService.getMinOrderQty(param.symbol);
            const rawQty = (walletBalance * param.equityRatio) / param.entryPrice;
            const stepCount = Math.floor(rawQty / qtyStep);
            const qty = stepCount * qtyStep;
            const finalQty = Math.max(Math.min(qty, maxOrderQty), minOrderQty).toFixed(mulQtyStep);
            return parseFloat(finalQty);
        }
        catch (error) {
            this.logger.error('Failed to calculate qty', (0, log_detail_util_1.logDetail)({
                class: 'TraderExecutorService',
                function: 'calculateQty',
                error,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.calculateQty(param);
        }
    }
};
exports.TraderExecutorService = TraderExecutorService;
exports.TraderExecutorService = TraderExecutorService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        backtest_multi_method_service_1.BacktestMultiMethodService,
        instrument_service_1.InstrumentService,
        adapter_service_1.AdapterService,
        trade_result_service_1.TradeResultService,
        method_service_1.MethodService,
        historical_cache_service_1.HistoricalCacheService])
], TraderExecutorService);
//# sourceMappingURL=trade-executor.service.js.map