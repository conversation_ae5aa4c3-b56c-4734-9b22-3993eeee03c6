{"version": 3, "file": "backtest.service.js", "sourceRoot": "", "sources": ["../../src/services/backtest.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AACvD,qCAAiC;AACjC,uEAA+C;AAC/C,6DAAqD;AAErD,2FAAkF;AAGlF,uEAA+D;AAC/D,yGAAgG;AAChG,+FAAqF;AAErF,yEAAoE;AACpE,2GAAgG;AAChG,+FAAqF;AACrF,iGAAuF;AACvF,+FAAqF;AACrF,2GAAgG;AAChG,2FAAkF;AAClF,iGAAuF;AAGhF,IAAM,eAAe,GAArB,MAAM,eAAe;IAE0B;IACjC;IAFnB,YACoD,MAAc,EAC/C,sBAA8C;QADb,WAAM,GAAN,MAAM,CAAQ;QAC/C,2BAAsB,GAAtB,sBAAsB,CAAwB;IAC9D,CAAC;IAEJ,KAAK,CAAC,oBAAoB,CAAC,KAAa;QACtC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,OAAO,GAAmB,EAAE,CAAC;YACnC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,uBAAuB,GAC3B,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;oBAC9C,MAAM;oBACN,QAAQ,EAAE,IAAA,wBAAc,EAAC,oBAAoB,CAAC;oBAC9C,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC9B,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,KAAK,EAAE,IAAA,wBAAc,EAAC,4BAA4B,CAAC;oBACnD,IAAI,EAAE,KAAK;iBACZ,CAAC,CAAC;gBAEL,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC;oBAClE,MAAM,MAAM,GAAG,IAAA,qCAAc,EAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;oBAC7D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B,EAC3B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,KAAa,EACb,mBAAkC;QAElC,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,MAAM;gBAAE,OAAO,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC/B,MAAM,OAAO,GAAmB,EAAE,CAAC;YAEnC,MAAM,uBAAuB,GAC3B,mBAAmB;gBACnB,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;oBAC/C,GAAG,KAAK;oBACR,MAAM;oBACN,QAAQ,EAAE,IAAA,wBAAc,EAAC,oBAAoB,CAAC;oBAC9C,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC9B,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,KAAK,EAAE,IAAA,wBAAc,EAAC,4BAA4B,CAAC;oBACnD,IAAI,EAAE,KAAK;iBACZ,CAAC,CAAC,CAAC;YAEN,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC;gBAClE,MAAM,MAAM,GAAG,IAAA,qCAAc,EAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;gBAC7D,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B,EAC3B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,KAAqB;QACxC,MAAM,WAAW,GAAwB;YACvC,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;YACrB,OAAO,EAAE,IAAI,IAAI,EAAE;YACnB,eAAe,EAAE,CAAC;YAClB,iBAAiB,EAAE,CAAC;YACpB,WAAW,EAAE,CAAC;YACd,eAAe,EAAE,CAAC;YAClB,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,CAAC;YACnB,kBAAkB,EAAE,CAAC;YACrB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,eAAe,EAAE,CAAC;YAClB,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,CAAC;YACnB,kBAAkB,EAAE,CAAC;YACrB,oBAAoB,EAAE,CAAC;YACvB,oBAAoB,EAAE,CAAC;YACvB,sBAAsB,EAAE,CAAC;YACzB,gBAAgB,EAAE,CAAC;YACnB,iBAAiB,EAAE,CAAC;YACpB,oBAAoB,EAAE,CAAC;YACvB,kBAAkB,EAAE,CAAC;SACtB,CAAC;QACF,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,MAAM;gBAAE,OAAO,WAAW,CAAC;YAEtC,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAC9B,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,CACnC,CAAC,MAAM,CAAC;YACT,IAAI,CAAC,WAAW;gBAAE,OAAO,WAAW,CAAC;YAErC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;YAClE,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;YAEpC,WAAW,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YACzC,WAAW,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACrC,WAAW,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YACjC,WAAW,CAAC,eAAe,GAAG,WAAW,GAAG,SAAS,CAAC;YACtD,WAAW,CAAC,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAC1C,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,CACpC,CAAC,MAAM,CAAC;YACT,WAAW,CAAC,sBAAsB,GAAG,IAAA,2DAAwB,EAAC,KAAK,CAAC,CAAC;YACrE,WAAW,CAAC,WAAW,GAAG,MAAM,CAC9B,CAAC,CAAC,WAAW,GAAG,WAAW,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAC/D,CAAC;YACF,WAAW,CAAC,eAAe,GAAG,IAAA,2DAAwB,EAAC,KAAK,CAAC,CAAC;YAC9D,WAAW,CAAC,cAAc,GAAG,IAAA,sEAA6B,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC1E,WAAW,CAAC,gBAAgB,GAAG,IAAA,sEAA6B,EAC1D,KAAK,EACL,QAAQ,CACT,CAAC;YACF,WAAW,CAAC,kBAAkB,GAAG,IAAA,wDAAuB,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACxE,WAAW,CAAC,oBAAoB,GAAG,IAAA,wDAAuB,EACxD,KAAK,EACL,QAAQ,CACT,CAAC;YACF,WAAW,CAAC,gBAAgB,GAAG,IAAA,6DAAyB,EAAC,KAAK,CAAC,CAAC;YAChE,WAAW,CAAC,eAAe,GAAG,IAAA,2DAAwB,EAAC,KAAK,CAAC,CAAC;YAC9D,WAAW,CAAC,cAAc,GAAG,IAAA,sEAA6B,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC1E,WAAW,CAAC,gBAAgB,GAAG,IAAA,sEAA6B,EAC1D,KAAK,EACL,QAAQ,CACT,CAAC;YACF,WAAW,CAAC,kBAAkB,GAAG,IAAA,wDAAuB,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACxE,WAAW,CAAC,oBAAoB,GAAG,IAAA,wDAAuB,EACxD,KAAK,EACL,QAAQ,CACT,CAAC;YACF,WAAW,CAAC,gBAAgB,GAAG,IAAA,6DAAyB,EAAC,KAAK,CAAC,CAAC;YAChE,WAAW,CAAC,oBAAoB,GAAG,IAAA,sEAA8B,EAAC,KAAK,CAAC,CAAC;YACzE,WAAW,CAAC,iBAAiB,GAAG,MAAM,CACpC,CACE,WAAW,CAAC,oBAAoB,GAAG,WAAW,CAAC,eAAe,CAC/D,CAAC,OAAO,CAAC,CAAC,CAAC,CACb,CAAC;YAEF,WAAW,CAAC,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAC1C,WAAW,CAAC,eAAe;gBACzB,IAAI,CAAC,IAAI,CACP,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;oBAC9D,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAC7B,CACJ,CAAC;YACF,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI,CACxC,WAAW,CAAC,oBAAoB,GAAG,EAAE,CACtC,CAAC;YAEF,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,EAChC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,qBAAqB;gBAC/B,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO,WAAW,CAAC;QACrB,CAAC;IACH,CAAC;CACF,CAAA;AA3LY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;QACvB,iDAAsB;GAHtD,eAAe,CA2L3B"}