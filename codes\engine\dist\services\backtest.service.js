"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BacktestService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const configurations_1 = __importDefault(require("../configurations"));
const log_detail_util_1 = require("../util/log-detail.util");
const calculate_max_consecutive_util_1 = require("../util/calculate-max-consecutive.util");
const generate_result_util_1 = require("../util/generate-result.util");
const calculate_cummulative_percentage_util_1 = require("../util/calculate-cummulative-percentage.util");
const calculate_reward_risk_ratio_util_1 = require("../util/calculate-reward-risk-ratio.util");
const historical_cache_service_1 = require("./historical-cache.service");
const calculate_max_stop_profit_percent_util_1 = require("../util/calculate-max-stop-profit-percent.util");
const calculate_max_open_position_util_1 = require("../util/calculate-max-open-position.util");
const calculate_max_holding_period_util_1 = require("../util/calculate-max-holding-period.util");
const calculate_avg_open_position_util_1 = require("../util/calculate-avg-open-position.util");
const calculate_avg_stop_profit_percent_util_1 = require("../util/calculate-avg-stop-profit-percent.util");
const calculate_avg_consecutive_util_1 = require("../util/calculate-avg-consecutive.util");
const calculate_avg_holding_period_utll_1 = require("../util/calculate-avg-holding-period.utll");
let BacktestService = class BacktestService {
    logger;
    historicalCacheService;
    constructor(logger, historicalCacheService) {
        this.logger = logger;
        this.historicalCacheService = historicalCacheService;
    }
    async getMultiSymbolResult(param) {
        try {
            const symbols = [...new Set(param.map((item) => item.symbol))];
            const results = [];
            for (const symbol of symbols) {
                const historicalExecutionData = await this.historicalCacheService.getHistorical({
                    symbol,
                    interval: (0, configurations_1.default)('EXECUTION_INTERVAL'),
                    start: new Date(param[0].date),
                    end: new Date(),
                    limit: (0, configurations_1.default)('HISTORICAL_EXECUTION_LIMIT'),
                    sort: 'ASC',
                });
                for (const item of param.filter((item) => item.symbol === symbol)) {
                    const result = (0, generate_result_util_1.generateResult)(item, historicalExecutionData);
                    results.push(result);
                }
            }
            return results;
        }
        catch (error) {
            this.logger.error('Failed to generate result', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'generateResult',
                param,
                error,
            }));
            return [];
        }
    }
    async getSingleSymbolResult(param, historicalExecution) {
        try {
            if (!param.length)
                return [];
            const symbol = param[0].symbol;
            const results = [];
            const historicalExecutionData = historicalExecution ??
                (await this.historicalCacheService.getHistorical({
                    ...param,
                    symbol,
                    interval: (0, configurations_1.default)('EXECUTION_INTERVAL'),
                    start: new Date(param[0].date),
                    end: new Date(),
                    limit: (0, configurations_1.default)('HISTORICAL_EXECUTION_LIMIT'),
                    sort: 'ASC',
                }));
            for (const item of param.filter((item) => item.symbol === symbol)) {
                const result = (0, generate_result_util_1.generateResult)(item, historicalExecutionData);
                results.push({ ...result, id: item.id });
            }
            return results;
        }
        catch (error) {
            this.logger.error('Failed to generate result', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'generateResult',
                param,
                error,
            }));
            return [];
        }
    }
    async getPerformance(param) {
        const performance = {
            methodId: '',
            fromDate: new Date(0),
            endDate: new Date(),
            totalValidTrade: 0,
            totalInvalidTrade: 0,
            probability: 0,
            avgOpenPosition: 0,
            avgStopPercent: 0,
            avgProfitPercent: 0,
            avgConsecutiveLoss: 0,
            avgConsecutiveProfit: 0,
            avgHoldingPeriod: 0,
            maxOpenPosition: 0,
            maxStopPercent: 0,
            maxProfitPercent: 0,
            maxConsecutiveLoss: 0,
            maxConsecutiveProfit: 0,
            cumulativePercentage: 0,
            averageRewardRiskRatio: 0,
            maxHoldingPeriod: 0,
            avgRevenueByTrade: 0,
            avgValidTradeByMonth: 0,
            avgValidTradeByDay: 0,
        };
        try {
            if (!param.length)
                return performance;
            const totalProfit = param.filter((item) => item.status === 'profit').length;
            if (!totalProfit)
                return performance;
            const lossResult = param.filter((item) => item.status === 'loss');
            const totalLoss = lossResult.length;
            performance.methodId = param[0].methodId;
            performance.fromDate = param[0].date;
            performance.endDate = new Date();
            performance.totalValidTrade = totalProfit + totalLoss;
            performance.totalInvalidTrade = param.filter((item) => item.status === 'invalid').length;
            performance.averageRewardRiskRatio = (0, calculate_reward_risk_ratio_util_1.calculateRewardRiskRatio)(param);
            performance.probability = Number(((totalProfit / performance.totalValidTrade) * 100).toFixed(2));
            performance.avgOpenPosition = (0, calculate_avg_open_position_util_1.calculateAvgOpenPosition)(param);
            performance.avgStopPercent = (0, calculate_avg_stop_profit_percent_util_1.calculateAvgStopProfitPercent)(param, 'stop');
            performance.avgProfitPercent = (0, calculate_avg_stop_profit_percent_util_1.calculateAvgStopProfitPercent)(param, 'profit');
            performance.avgConsecutiveLoss = (0, calculate_avg_consecutive_util_1.calculateAvgConsecutive)(param, 'loss');
            performance.avgConsecutiveProfit = (0, calculate_avg_consecutive_util_1.calculateAvgConsecutive)(param, 'profit');
            performance.avgHoldingPeriod = (0, calculate_avg_holding_period_utll_1.calculateAvgHoldingPeriod)(param);
            performance.maxOpenPosition = (0, calculate_max_open_position_util_1.calculateMaxOpenPosition)(param);
            performance.maxStopPercent = (0, calculate_max_stop_profit_percent_util_1.calculateMaxStopProfitPercent)(param, 'stop');
            performance.maxProfitPercent = (0, calculate_max_stop_profit_percent_util_1.calculateMaxStopProfitPercent)(param, 'profit');
            performance.maxConsecutiveLoss = (0, calculate_max_consecutive_util_1.calculateMaxConsecutive)(param, 'loss');
            performance.maxConsecutiveProfit = (0, calculate_max_consecutive_util_1.calculateMaxConsecutive)(param, 'profit');
            performance.maxHoldingPeriod = (0, calculate_max_holding_period_util_1.calculateMaxHoldingPeriod)(param);
            performance.cumulativePercentage = (0, calculate_cummulative_percentage_util_1.calculateCummulativePercentage)(param);
            performance.avgRevenueByTrade = Number((performance.cumulativePercentage / performance.totalValidTrade).toFixed(2));
            performance.avgValidTradeByMonth = Math.ceil(performance.totalValidTrade /
                Math.ceil((performance.endDate.getTime() - performance.fromDate.getTime()) /
                    (1000 * 60 * 60 * 24 * 30)));
            performance.avgValidTradeByDay = Math.ceil(performance.avgValidTradeByMonth / 30);
            return performance;
        }
        catch (error) {
            this.logger.error('Failed to generate performance', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'generatePerformance',
                param,
                error,
            }));
            return performance;
        }
    }
};
exports.BacktestService = BacktestService;
exports.BacktestService = BacktestService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        historical_cache_service_1.HistoricalCacheService])
], BacktestService);
//# sourceMappingURL=backtest.service.js.map