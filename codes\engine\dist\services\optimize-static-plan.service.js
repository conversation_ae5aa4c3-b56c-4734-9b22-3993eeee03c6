"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimizeStaticPlanService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const backtest_service_1 = require("./backtest.service");
const configurations_1 = __importDefault(require("../configurations"));
const pattern_service_1 = require("./pattern.service");
const static_plan_service_1 = require("./static-plan.service");
const instrument_service_1 = require("./instrument.service");
const log_detail_util_1 = require("../util/log-detail.util");
const historical_cache_service_1 = require("./historical-cache.service");
let OptimizeStaticPlanService = class OptimizeStaticPlanService {
    logger;
    backtestService;
    historicalCacheService;
    patternService;
    staticPlanService;
    instrumentService;
    constructor(logger, backtestService, historicalCacheService, patternService, staticPlanService, instrumentService) {
        this.logger = logger;
        this.backtestService = backtestService;
        this.historicalCacheService = historicalCacheService;
        this.patternService = patternService;
        this.staticPlanService = staticPlanService;
        this.instrumentService = instrumentService;
    }
    async optimize(param, historical, historicalExecution, patterns, instrument, plan, results) {
        try {
            const historicalData = historical ?? (await this.historicalCacheService.getHistorical(param));
            const historicalExecutionData = historicalExecution ??
                (await this.historicalCacheService.getHistorical({
                    ...param,
                    end: new Date(),
                    interval: (0, configurations_1.default)('EXECUTION_INTERVAL'),
                }));
            const patternData = patterns ??
                (await this.patternService.getPattern(param, historicalData));
            const instrumentData = instrument ??
                (await this.instrumentService.getInstrument({
                    symbol: param.symbol,
                }));
            const planData = plan ??
                (await this.staticPlanService.getPlan(param, historicalData, patternData, instrumentData));
            const resultsData = results ??
                (await this.backtestService.getSingleSymbolResult(planData, historicalExecutionData));
            const optimizedPlan = {
                entryPercentByClose: 0,
                rewardPercent: 0,
                riskPercent: 0,
            };
            if (!resultsData.length)
                return optimizedPlan;
            const profitResults = resultsData.filter((item) => item.status === 'profit');
            if (!profitResults.length)
                return optimizedPlan;
            const targetEntryList = [];
            const targetProfitList = [];
            const targetLossList = [];
            for (const item of profitResults) {
                const pattern = historicalData.find((h) => h.date.getTime() === item.date.getTime());
                if (!pattern)
                    continue;
                const historicalDataForExecution = historicalData.filter((h) => h.date.getTime() > item.date.getTime());
                if (!historicalDataForExecution.length)
                    continue;
                const startExecution = historicalDataForExecution[0];
                const historicalExecutionSample = historicalExecutionData.filter((historicalItem) => historicalItem.date.getTime() >= startExecution.date.getTime());
                if (!historicalExecutionSample.length)
                    continue;
                let stopDate = new Date();
                const stopItem = historicalExecutionSample.filter((h) => param.orderType === 'long'
                    ? h.low <= item.stopLoss
                    : h.high >= item.stopLoss);
                if (stopItem.length) {
                    stopDate = stopItem[0].date;
                }
                const historicalExecutionSampleForProfit = historicalExecutionSample.filter((h) => h.date.getTime() < stopDate.getTime());
                if (historicalExecutionSampleForProfit.length === 0)
                    continue;
                const profitItem = param.orderType === 'long'
                    ? historicalExecutionSampleForProfit.reduce((prev, curr) => prev.high > curr.high ? prev : curr)
                    : historicalExecutionSampleForProfit.reduce((prev, curr) => prev.low < curr.low ? prev : curr);
                const profitRatio = (param.orderType === 'long' ? profitItem.high : profitItem.low) /
                    pattern.close;
                targetProfitList.push(profitRatio);
                const historicalExecutionSampleForStop = historicalExecutionSampleForProfit.filter((h) => h.date.getTime() < profitItem.date.getTime());
                if (historicalExecutionSampleForStop.length === 0)
                    continue;
                const lossItem = param.orderType === 'long'
                    ? historicalExecutionSampleForStop.reduce((prev, curr) => prev.low < curr.low ? prev : curr)
                    : historicalExecutionSampleForStop.reduce((prev, curr) => prev.high > curr.high ? prev : curr);
                const lossRatio = (param.orderType === 'long' ? lossItem.low : lossItem.high) /
                    pattern.close;
                targetLossList.push(lossRatio);
                const historicalExecutionSampleForEntry = historicalExecutionSampleForProfit.filter((h) => h.date.getTime() < item.expiryDate.getTime());
                if (!historicalExecutionSampleForEntry.length)
                    continue;
                const entryItem = item.orderType === 'long'
                    ? historicalExecutionSampleForEntry.reduce((prev, curr) => prev.low < curr.low ? prev : curr)
                    : historicalExecutionSampleForEntry.reduce((prev, curr) => prev.high > curr.high ? prev : curr);
                const entryRatio = (item.orderType === 'long' ? entryItem.low : entryItem.high) /
                    pattern.close;
                targetEntryList.push(entryRatio);
            }
            if (!targetProfitList.length ||
                !targetLossList.length ||
                !targetEntryList.length) {
                return optimizedPlan;
            }
            const entryPercentByClose = 100 *
                (param.orderType === 'long'
                    ? Math.max(...targetEntryList) - 1
                    : Math.min(...targetEntryList) - 1);
            const rewardPercent = 100 *
                (param.orderType === 'long'
                    ? Math.min(...targetProfitList) / Math.max(...targetEntryList) - 1
                    : Math.max(...targetProfitList) / Math.min(...targetEntryList) - 1);
            const initialRiskPercent = 100 *
                (param.orderType === 'long'
                    ? Math.min(...targetLossList) / Math.max(...targetEntryList) - 1
                    : Math.max(...targetLossList) / Math.min(...targetEntryList) - 1);
            const riskPercent = param.orderType === 'long' && initialRiskPercent > -0.01
                ? initialRiskPercent - 0.01
                : param.orderType === 'short' && initialRiskPercent < 0.01
                    ? initialRiskPercent + 0.01
                    : initialRiskPercent;
            return {
                entryPercentByClose,
                rewardPercent,
                riskPercent,
            };
        }
        catch (error) {
            this.logger.error('Failed to fetch pattern data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getPlan',
                param,
                error,
            }));
            return {
                entryPercentByClose: 0,
                rewardPercent: 0,
                riskPercent: 0,
            };
        }
    }
    async getOptimizedStaticPlan(param, historical, historicalExecution, patterns, instrument, plan, results) {
        try {
            const historicalData = historical ?? (await this.historicalCacheService.getHistorical(param));
            const historicalExecutionData = historicalExecution ??
                (await this.historicalCacheService.getHistorical({
                    ...param,
                    end: new Date(),
                    interval: (0, configurations_1.default)('EXECUTION_INTERVAL'),
                }));
            const patternData = patterns ??
                (await this.patternService.getPattern(param, historicalData));
            const instrumentData = instrument ??
                (await this.instrumentService.getInstrument({
                    symbol: param.symbol,
                }));
            const planData = plan ??
                (await this.staticPlanService.getPlan(param, historicalData, patternData, instrumentData));
            const resultsData = results ??
                (await this.backtestService.getSingleSymbolResult(planData, historicalExecutionData));
            const optimizedParam = await this.optimize(param, historicalData, historicalExecutionData, patternData, instrumentData, planData, resultsData);
            const optimizedPlan = await this.staticPlanService.getPlan({ ...param, ...optimizedParam }, historicalData, patternData, instrumentData);
            return optimizedPlan;
        }
        catch (error) {
            this.logger.error('Failed to fetch pattern data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getPlan',
                param,
                error,
            }));
            return [];
        }
    }
};
exports.OptimizeStaticPlanService = OptimizeStaticPlanService;
exports.OptimizeStaticPlanService = OptimizeStaticPlanService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        backtest_service_1.BacktestService,
        historical_cache_service_1.HistoricalCacheService,
        pattern_service_1.PatternService,
        static_plan_service_1.StaticPlanService,
        instrument_service_1.InstrumentService])
], OptimizeStaticPlanService);
//# sourceMappingURL=optimize-static-plan.service.js.map