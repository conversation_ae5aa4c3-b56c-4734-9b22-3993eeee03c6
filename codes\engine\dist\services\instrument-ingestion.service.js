"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InstrumentIngestionService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const adapter_service_1 = require("./adapter.service");
const instrument_service_1 = require("./instrument.service");
const to_milliseconds_util_1 = require("../util/to-milliseconds.util");
const delay_util_1 = require("../util/delay.util");
const get_symbols_slice_util_1 = require("../util/get-symbols-slice.util");
const configurations_1 = __importDefault(require("../configurations"));
const method_status_service_1 = require("./method-status.service");
let InstrumentIngestionService = class InstrumentIngestionService {
    adapterService;
    instrumentService;
    methodStatusService;
    logger;
    constructor(adapterService, instrumentService, methodStatusService, logger) {
        this.adapterService = adapterService;
        this.instrumentService = instrumentService;
        this.methodStatusService = methodStatusService;
        this.logger = logger;
    }
    async ingest(enableListedTime) {
        try {
            const disableCluster = (0, configurations_1.default)('DISABLE_CLUSTER');
            const engineMode = (0, configurations_1.default)('ENGINE_MODE');
            const symbolsArray = await this.getSymbols(engineMode, disableCluster);
            const symbolsSet = new Set(symbolsArray);
            const newData = await this.adapterService.fetchBybitInstrument();
            const filteredData = newData.filter((item) => symbolsSet.has(item.symbol));
            const completedData = enableListedTime
                ? await this.ingestListedTime(filteredData)
                : filteredData;
            await this.instrumentService.insert(completedData, enableListedTime);
            return;
        }
        catch (error) {
            this.logger.error('Ingestion process failed', (0, log_detail_util_1.logDetail)({
                class: 'InstrumentIngestionService',
                function: 'ingest',
                error,
            }));
            return;
        }
    }
    async getSymbols(engineMode, disableCluster) {
        let symbols;
        if (engineMode === 'worker') {
            symbols = [
                ...new Set((await this.adapterService.fetchBybitInstrument()).map((item) => item.symbol)),
            ];
        }
        else {
            const statuses = await this.methodStatusService.getAllMethodStatus();
            symbols = [...new Set(statuses.map((item) => item.symbol))];
        }
        return disableCluster === 'false' ? (0, get_symbols_slice_util_1.getSymbolsSlice)(symbols) : symbols;
    }
    async ingestListedTime(newData) {
        const results = [];
        const cpuAllocation = (0, configurations_1.default)('CPU_ALLOCATION') ?? (0, configurations_1.default)('TOTAL_CORE');
        const defaultLimit = (0, configurations_1.default)('DEFAULT_LIMIT');
        const oneDayMs = (0, to_milliseconds_util_1.toMiliseconds)('D');
        for (const data of newData) {
            const now = Date.now();
            for (let t = now; t > 0; t -= oneDayMs * defaultLimit) {
                const start = new Date(t - oneDayMs * defaultLimit);
                const end = new Date(t);
                const historical = await this.adapterService.fetchBybitHistorical({
                    symbol: data.symbol,
                    interval: 'D',
                    start,
                    end,
                    limit: defaultLimit,
                    sort: 'ASC',
                });
                if (historical.length) {
                    data.listedTime = new Date(historical[0].date).getTime() + oneDayMs;
                    await (0, delay_util_1.delay)((1000 / 50) * cpuAllocation);
                }
                else {
                    results.push(data);
                    break;
                }
            }
        }
        return results;
    }
};
exports.InstrumentIngestionService = InstrumentIngestionService;
exports.InstrumentIngestionService = InstrumentIngestionService = __decorate([
    (0, common_1.Injectable)(),
    __param(3, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [adapter_service_1.AdapterService,
        instrument_service_1.InstrumentService,
        method_status_service_1.MethodStatusService,
        winston_1.Logger])
], InstrumentIngestionService);
//# sourceMappingURL=instrument-ingestion.service.js.map