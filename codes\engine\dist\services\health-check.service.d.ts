import { Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
export declare class HealthCheckService {
    private readonly logger;
    private readonly dataSource;
    private static readonly TIMEOUT_MS;
    private readonly healthCheckUrls;
    private isApplicationReady;
    constructor(logger: Logger, dataSource: DataSource);
    private checkDatabase;
    private checkUrl;
    checkStartup(): Promise<{
        timestamp: string;
        results: Record<string, 'reachable' | 'unreachable'>;
    }>;
    checkReadiness(): Promise<{
        timestamp: string;
        results: Record<string, 'reachable' | 'unreachable'>;
    }>;
    check(): Promise<{
        timestamp: string;
        results: Record<string, 'reachable' | 'unreachable'>;
    }>;
}
