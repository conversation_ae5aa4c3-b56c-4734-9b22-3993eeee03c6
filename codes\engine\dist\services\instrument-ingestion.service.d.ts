import { Logger } from 'winston';
import { AdapterService } from './adapter.service';
import { InstrumentService } from './instrument.service';
import { MethodStatusService } from './method-status.service';
export declare class InstrumentIngestionService {
    private readonly adapterService;
    private readonly instrumentService;
    private readonly methodStatusService;
    private readonly logger;
    constructor(adapterService: AdapterService, instrumentService: InstrumentService, methodStatusService: MethodStatusService, logger: Logger);
    ingest(enableListedTime?: boolean): Promise<void>;
    private getSymbols;
    private ingestListedTime;
}
