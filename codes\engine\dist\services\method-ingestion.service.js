"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MethodIngestionService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const configurations_1 = __importDefault(require("../configurations"));
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const backtest_method_service_1 = require("./backtest-method.service");
const method_service_1 = require("./method.service");
const instrument_service_1 = require("./instrument.service");
const pattern_service_1 = require("./pattern.service");
const uuid_1 = require("uuid");
const method_status_service_1 = require("./method-status.service");
const optimize_static_plan_service_1 = require("./optimize-static-plan.service");
const is_good_method_util_1 = require("../util/is-good-method.util");
const historical_cache_service_1 = require("./historical-cache.service");
const suffle_array_util_1 = require("../util/suffle-array.util");
let MethodIngestionService = class MethodIngestionService {
    logger;
    backtestMethodService;
    methodService;
    instrumentService;
    patternService;
    historicalCacheService;
    optimizeStaticPlanService;
    methodStatusService;
    constructor(logger, backtestMethodService, methodService, instrumentService, patternService, historicalCacheService, optimizeStaticPlanService, methodStatusService) {
        this.logger = logger;
        this.backtestMethodService = backtestMethodService;
        this.methodService = methodService;
        this.instrumentService = instrumentService;
        this.patternService = patternService;
        this.historicalCacheService = historicalCacheService;
        this.optimizeStaticPlanService = optimizeStaticPlanService;
        this.methodStatusService = methodStatusService;
    }
    async ingestForever(symbols) {
        while (true) {
            try {
                await this.ingest(symbols);
                return;
            }
            catch (error) {
                this.logger.error('Ingestion loop error', error);
                await new Promise((resolve) => setTimeout(resolve, 5000));
                return;
            }
        }
    }
    async run(param, historicalData, historicalExecutionData, patternData, instrumentData) {
        try {
            if (!historicalData ||
                !historicalData.length ||
                !historicalExecutionData ||
                !historicalExecutionData.length ||
                !patternData ||
                !patternData.length ||
                !instrumentData ||
                !instrumentData.length)
                return;
            const key = `${param.symbol}-${param.interval}-${param.patternType}-${param.pattern}-${param.validityPeriod}-${param.lookbackPeriod}-${param.riskPercent}-${param.methodType}-${param.entryPercentByClose}-${param.rewardPercent}-${param.orderType}-${param.trend}`;
            const methodId = (0, uuid_1.v5)(key, (0, configurations_1.default)('UUID_NAMESPACE'));
            param.methodId = methodId;
            const existingMethodStatus = await this.methodStatusService.getMethodStatusByMethodId(methodId);
            if (existingMethodStatus)
                return;
            const { result, performance } = await this.backtestMethodService.getBacktestMethodBoth(param, historicalData, historicalExecutionData, patternData, instrumentData);
            if ((0, is_good_method_util_1.isGoodMethod)(result, performance)) {
                if (param.methodType === 'static' && param.enableOptimization) {
                    const optimizedPlan = await this.optimizeStaticPlanService.optimize(param, historicalData, historicalExecutionData, patternData, instrumentData, undefined, result);
                    param = { ...param, ...optimizedPlan };
                }
                await this.methodService.insertMethodParam({
                    ...param,
                    enableOptimization: param.methodType === 'dynamic',
                });
                await this.methodService.insertMethodPerformance(performance);
                await this.methodService.insertMethodResult(result);
                await this.methodStatusService.insertMethodStatus(methodId, param.symbol, param.interval);
            }
            return;
        }
        catch (error) {
            this.logger.error('Ingestion process failed', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'ingest',
                error,
            }));
            return;
        }
    }
    async ingest(symbols) {
        try {
            const limit = (0, configurations_1.default)('HISTORICAL_EXECUTION_LIMIT');
            const sort = 'ASC';
            const methodTypes = (0, suffle_array_util_1.shuffleArray)((0, configurations_1.default)('METHOD_TYPE'));
            const lookbackPeriods = (0, suffle_array_util_1.shuffleArray)((0, configurations_1.default)('METHOD_SIGNAL_LOOKBACK_PERIOD'));
            const intervals = (0, configurations_1.default)('INTERVALS');
            const validityPeriods = (0, suffle_array_util_1.shuffleArray)((0, configurations_1.default)('METHOD_SIGNAL_VALIDITY_PERIOD'));
            const riskPercentSamples = (0, suffle_array_util_1.shuffleArray)((0, configurations_1.default)('METHOD_RISK_PERCENT_SAMPLES'));
            const orderTypes = (0, suffle_array_util_1.shuffleArray)((0, configurations_1.default)('ORDER_TYPES'));
            const patterns = (0, suffle_array_util_1.shuffleArray)((0, configurations_1.default)('CANDLESTICK_PATTERNS'));
            const trends = (0, suffle_array_util_1.shuffleArray)((0, configurations_1.default)('TRENDS'));
            const executionIntervals = (0, configurations_1.default)('EXECUTION_INTERVAL');
            const rewardPercentSamples = (0, suffle_array_util_1.shuffleArray)((0, configurations_1.default)('METHOD_REWARD_PERCENT_SAMPLES'));
            const entryPercentByCloses = (0, suffle_array_util_1.shuffleArray)((0, configurations_1.default)('METHOD_ENTRY_PERCENT_BY_CLOSE_SAMPLES'));
            const start = new Date(0);
            const end = new Date();
            for (const symbol of symbols) {
                const instrumentData = await this.instrumentService.getInstrument({
                    symbol,
                });
                if (!instrumentData.length)
                    continue;
                const historicalExecutionData = await this.historicalCacheService.getHistorical({
                    symbol,
                    start,
                    interval: executionIntervals,
                    end,
                    limit,
                    sort,
                });
                if (!historicalExecutionData.length)
                    continue;
                for (const interval of intervals
                    .filter((item) => item !== executionIntervals)
                    .reverse()) {
                    const historicalData = await this.historicalCacheService.getHistorical({
                        symbol,
                        start,
                        interval,
                        end,
                        limit,
                        sort,
                    });
                    if (!historicalData.length)
                        continue;
                    for (const trend of trends) {
                        const patternType = 'candlestick';
                        for (const pattern of patterns) {
                            const patternData = await this.patternService.getPattern({
                                symbol,
                                interval,
                                patternType,
                                pattern,
                                start,
                                end,
                                trend,
                                limit,
                                sort,
                            }, historicalData);
                            if (!patternData.length)
                                continue;
                            for (const validityPeriod of validityPeriods) {
                                for (const entryPercentByClose of entryPercentByCloses) {
                                    for (const orderType of orderTypes) {
                                        for (const riskPercent of riskPercentSamples) {
                                            for (const rewardPercent of rewardPercentSamples.filter((item) => item >= riskPercent)) {
                                                for (const methodType of methodTypes) {
                                                    this.logger.info(`Running ${symbol}-${interval}-${patternType}-${pattern}-${validityPeriod}-${entryPercentByClose}-${riskPercent}-${rewardPercent}-${orderType}-${trend}-${methodType}`);
                                                    if (methodType === 'dynamic') {
                                                        for (const lookbackPeriod of lookbackPeriods) {
                                                            await this.run({
                                                                symbol,
                                                                interval,
                                                                patternType,
                                                                pattern,
                                                                validityPeriod,
                                                                lookbackPeriod,
                                                                riskPercent,
                                                                methodType,
                                                                entryPercentByClose,
                                                                rewardPercent,
                                                                start,
                                                                end,
                                                                orderType,
                                                                trend,
                                                                limit,
                                                                sort,
                                                                enableOptimization: true,
                                                            }, historicalData, historicalExecutionData, patternData, instrumentData);
                                                        }
                                                    }
                                                    else {
                                                        await this.run({
                                                            symbol,
                                                            interval,
                                                            patternType,
                                                            pattern,
                                                            validityPeriod,
                                                            lookbackPeriod: 0,
                                                            riskPercent,
                                                            methodType: 'static',
                                                            entryPercentByClose,
                                                            rewardPercent,
                                                            start,
                                                            end,
                                                            orderType,
                                                            trend,
                                                            limit,
                                                            sort,
                                                            enableOptimization: true,
                                                        }, historicalData, historicalExecutionData, patternData, instrumentData);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return process.exit(0);
        }
        catch (error) {
            this.logger.error('Ingestion process failed', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'ingest',
                error,
            }));
            return;
        }
    }
};
exports.MethodIngestionService = MethodIngestionService;
exports.MethodIngestionService = MethodIngestionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        backtest_method_service_1.BacktestMethodService,
        method_service_1.MethodService,
        instrument_service_1.InstrumentService,
        pattern_service_1.PatternService,
        historical_cache_service_1.HistoricalCacheService,
        optimize_static_plan_service_1.OptimizeStaticPlanService,
        method_status_service_1.MethodStatusService])
], MethodIngestionService);
//# sourceMappingURL=method-ingestion.service.js.map