import { Logger } from 'winston';
import { BacktestMethodService } from './backtest-method.service';
import { MethodService } from './method.service';
import { InstrumentService } from './instrument.service';
import { PatternService } from './pattern.service';
import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { Historical } from 'src/interface/historical.interface';
import { Pattern } from 'src/interface/pattern.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { MethodStatusService } from './method-status.service';
import { OptimizeStaticPlanService } from './optimize-static-plan.service';
import { HistoricalCacheService } from './historical-cache.service';
export declare class MethodIngestionService {
    private readonly logger;
    private readonly backtestMethodService;
    private readonly methodService;
    private readonly instrumentService;
    private readonly patternService;
    private readonly historicalCacheService;
    private readonly optimizeStaticPlanService;
    private readonly methodStatusService;
    constructor(logger: Logger, backtestMethodService: BacktestMethodService, methodService: MethodService, instrumentService: InstrumentService, patternService: PatternService, historicalCacheService: HistoricalCacheService, optimizeStaticPlanService: OptimizeStaticPlanService, methodStatusService: MethodStatusService);
    ingestForever(symbols: string[]): Promise<void>;
    run(param: GetBacktestMethodDto, historicalData?: Historical[], historicalExecutionData?: Historical[], patternData?: Pattern[], instrumentData?: Instrument[]): Promise<void>;
    ingest(symbols: string[]): Promise<undefined>;
}
