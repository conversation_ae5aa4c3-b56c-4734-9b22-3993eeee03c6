import { InsertResult, Repository, UpdateResult } from 'typeorm';
import { Logger } from 'winston';
import { MethodParamEntity } from 'src/entity/method-param.entity';
import { MethodResultEntity } from 'src/entity/method-result.entity';
import { MethodPerformanceEntity } from 'src/entity/method-performance.entity';
import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { GetMethodDto } from 'src/dto/get-method.dto';
import { MethodResult } from 'src/interface/method-result.interface';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { GetBacktestMultiMethodDto } from 'src/dto/get-backtest-multi-method.dto';
export declare class MethodService {
    private readonly MethodParamsRepository;
    private readonly MethodResultRepository;
    private readonly MethodPerformanceRepository;
    private readonly logger;
    constructor(MethodParamsRepository: Repository<MethodParamEntity>, MethodResultRepository: Repository<MethodResultEntity>, MethodPerformanceRepository: Repository<MethodPerformanceEntity>, logger: Logger);
    deleteMethodResultNotIn(methodIds: string[]): Promise<void>;
    deleteMethodPerformanceNotIn(methodIds: string[]): Promise<void>;
    getMethodIds(): Promise<MethodParamEntity[]>;
    insertMethodParam(param: GetBacktestMethodDto): Promise<InsertResult>;
    insertMethodResult(param: MethodResult[]): Promise<InsertResult>;
    insertMethodPerformance(param: BacktestPerformance): Promise<InsertResult | UpdateResult>;
    getParam(param: GetMethodDto): Promise<MethodParamEntity[]>;
    getResult(param: GetMethodDto): Promise<MethodResultEntity[]>;
    getResultById(id: string): Promise<MethodResultEntity | null>;
    getPendingOrOpenResult(): Promise<MethodResultEntity[]>;
    getPerformance(param: GetMethodDto): Promise<MethodPerformanceEntity[]>;
    private getMethodsByPerformance;
    getMultiMethodResult(param: GetBacktestMultiMethodDto): Promise<any[]>;
    deleteMethodResult(methodId: string): Promise<void>;
    deleteMethodPerformance(methodId: string): Promise<void>;
    deleteMethodParam(methodId: string): Promise<void>;
}
