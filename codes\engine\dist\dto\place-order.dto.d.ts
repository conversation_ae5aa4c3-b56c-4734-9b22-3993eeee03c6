export declare class PlaceOrderDto {
    category: 'spot' | 'linear' | 'inverse' | 'option';
    symbol: string;
    isLeverage?: 0 | 1;
    side: 'Buy' | 'Sell';
    orderType: 'Market' | 'Limit';
    qty: string;
    marketUnit?: 'baseCoin' | 'quoteCoin';
    slippageToleranceType?: string;
    slippageTolerance?: string;
    price?: string;
    triggerDirection?: 1 | 2;
    orderFilter?: 'Order' | 'tpslOrder' | 'StopOrder';
    triggerPrice?: string;
    triggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';
    orderIv?: string;
    timeInForce?: 'GTC' | 'IOC' | 'FOK' | 'PostOnly' | 'RPI';
    positionIdx?: 0 | 1 | 2;
    orderLinkId?: string;
    takeProfit?: string;
    stopLoss?: string;
    tpTriggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';
    slTriggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';
    reduceOnly?: boolean;
    closeOnTrigger?: boolean;
    smpType?: 'None' | 'CancelMaker' | 'CancelTaker' | 'CancelBoth';
    mmp?: boolean;
    tpslMode?: 'Full' | 'Partial';
    tpLimitPrice?: string;
    slLimitPrice?: string;
    tpOrderType?: 'Market' | 'Limit';
    slOrderType?: 'Market' | 'Limit';
}
