"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeResultService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const trade_result_entity_1 = require("../entity/trade-result.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
let TradeResultService = class TradeResultService {
    logger;
    tradeResultRepository;
    constructor(logger, tradeResultRepository) {
        this.logger = logger;
        this.tradeResultRepository = tradeResultRepository;
    }
    async getByorderLinkId(orderLinkId) {
        try {
            const queryBuilder = this.tradeResultRepository.createQueryBuilder('trade-result');
            return await queryBuilder
                .where('trade-result.orderLinkId = :orderLinkId', { orderLinkId })
                .getMany();
        }
        catch (err) {
            this.logger.error('Failed to read trade-result data', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultService',
                function: 'getByorderLinkId',
                error: err,
            }));
            return [];
        }
    }
    async save(param) {
        try {
            await this.tradeResultRepository.upsert(param, ['orderLinkId']);
        }
        catch (error) {
            this.logger.error('Failed to upsert', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultService',
                function: 'save',
                error,
            }));
            return;
        }
    }
    async getPendingResults() {
        try {
            const queryBuilder = this.tradeResultRepository.createQueryBuilder('trade-result');
            queryBuilder.andWhere('trade-result.status = :status', {
                status: 'pending',
            });
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read trade-result data', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultService',
                function: 'getPendingResults',
                error: err,
            }));
            return [];
        }
    }
    async getTradeResultByOrderId(orderId) {
        try {
            const queryBuilder = this.tradeResultRepository.createQueryBuilder('trade-result');
            return await queryBuilder
                .where('trade-result.orderId = :orderId', { orderId })
                .getMany();
        }
        catch (err) {
            this.logger.error('Failed to read trade-result data', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultService',
                function: 'getTradeResultByOrderId',
                error: err,
            }));
            return [];
        }
    }
    async getTradeResultByPrice(param) {
        try {
            const queryBuilder = this.tradeResultRepository.createQueryBuilder('trade-result');
            queryBuilder.andWhere('trade-result.price = :price', {
                price: param.price,
            });
            queryBuilder.andWhere('trade-result.side = :side', { side: param.side });
            queryBuilder.andWhere('trade-result.stopLoss = :stopLoss', {
                stopLoss: param.stopLoss,
            });
            queryBuilder.andWhere('trade-result.takeProfit = :takeProfit', {
                takeProfit: param.takeProfit,
            });
            queryBuilder.andWhere('trade-result.symbol = :symbol', {
                symbol: param.symbol,
            });
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read trade-result data', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultService',
                function: 'getTradeResultByPrice',
                error: err,
            }));
            return [];
        }
    }
    async getActiveResults() {
        try {
            const columnExists = await this.checkOrderIdColumnExists();
            if (!columnExists) {
                this.logger.warn('orderId column does not exist in trade-result table, using fallback query', (0, log_detail_util_1.logDetail)({
                    class: 'TradeResultService',
                    function: 'getActiveResults',
                }));
                return await this.tradeResultRepository.find({
                    where: [{ status: 'open' }, { status: 'pending' }],
                });
            }
            const queryBuilder = this.tradeResultRepository.createQueryBuilder('trade-result');
            queryBuilder.andWhere('trade-result.status = :status1 OR trade-result.status = :status2', {
                status1: 'open',
                status2: 'pending',
            });
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read trade-result data', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultService',
                function: 'getActiveResults',
                error: err,
            }));
            return [];
        }
    }
    async checkOrderIdColumnExists() {
        try {
            const queryRunner = this.tradeResultRepository.manager.connection.createQueryRunner();
            const result = await queryRunner.query(`
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'trade-result'
        AND column_name = 'orderId'
      `);
            await queryRunner.release();
            return result.length > 0;
        }
        catch (error) {
            this.logger.warn('Failed to check orderId column existence', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultService',
                function: 'checkOrderIdColumnExists',
                error,
            }));
            return false;
        }
    }
    async getClosedResults(limit) {
        try {
            const queryBuilder = this.tradeResultRepository.createQueryBuilder('trade-result');
            queryBuilder.andWhere('trade-result.status != :status1 AND trade-result.status != :status2', {
                status1: 'pending',
                status2: 'open',
            });
            queryBuilder.orderBy('trade-result.updatedAt', 'DESC');
            queryBuilder.limit(limit);
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read trade-result data', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultService',
                function: 'getClosedResults',
                error: err,
            }));
            return [];
        }
    }
    async updateStatus(param) {
        try {
            await this.tradeResultRepository.update({ orderLinkId: param.orderLinkId }, { ...param, updatedAt: new Date() });
        }
        catch (error) {
            this.logger.error('Failed to update', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultService',
                function: 'updateStatus',
                error,
            }));
            return;
        }
        return;
    }
};
exports.TradeResultService = TradeResultService;
exports.TradeResultService = TradeResultService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __param(1, (0, typeorm_2.InjectRepository)(trade_result_entity_1.TradeResultEntity)),
    __metadata("design:paramtypes", [winston_1.Logger,
        typeorm_1.Repository])
], TradeResultService);
//# sourceMappingURL=trade-result.service.js.map