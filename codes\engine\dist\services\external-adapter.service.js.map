{"version": 3, "file": "external-adapter.service.js", "sourceRoot": "", "sources": ["../../src/services/external-adapter.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AACvD,qCAAiC;AACjC,kDAA6C;AAC7C,uEAAgD;AAChD,6DAAqD;AAO9C,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAMmB;IALnC,UAAU,CAAgB;IAC1B,OAAO,CAAS;IAChB,MAAM,CAAS;IAEhC,YACoD,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;QAEhE,IAAI,CAAC,OAAO,GAAG,IAAA,wBAAc,EAAC,sBAAsB,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,IAAA,wBAAc,EAAC,0BAA0B,CAAC,CAAC;QAEzD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,+DAA+D,EAC/D,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,aAAa;aACxB,CAAC,CACH,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,mEAAmE,EACnE,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,aAAa;aACxB,CAAC,CACH,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,eAAK,CAAC,MAAM,CAAC;YAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,aAAa,EAAE,UAAU,IAAI,CAAC,MAAM,EAAE;aACvC;SACF,CAAC,CAAC;QAGH,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACtC,CAAC,MAAM,EAAE,EAAE;YACT,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,0BAA0B,EAC1B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,SAAS;gBACnB,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE;aACnE,CAAC,CACH,CAAC;YACF,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,EAChC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CACH,CAAC;YACF,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;QAGF,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACvC,CAAC,QAAQ,EAAE,EAAE;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B,EAC3B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,UAAU;gBACpB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE;aACzD,CAAC,CACH,CAAC;YACF,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,EACjC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,UAAU;gBACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,GAAG;gBACtB,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO;aAC7C,CAAC,CACH,CAAC;YACF,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAU;QAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CACzC,+BAA+B,EAC/B,KAAK,CACN,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAU;QAC1B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC5E,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAC3E,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,KAAU;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC3E,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,KAAU;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC3E,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAU;QAC1B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC5E,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAU;QAC9B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CACzC,wBAAwB,EACxB,KAAK,CACN,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAU;QAC9B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CACzC,wBAAwB,EACxB,KAAK,CACN,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,KAAU;QAC5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAE1E,OAAO,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;YACxC,GAAG,IAAI;YACP,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;SAC1B,CAAC,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,KAAU;QAC5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC1E,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;CACF,CAAA;AAjKY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAOR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;GANvD,sBAAsB,CAiKlC"}