import { Logger } from 'winston';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { Historical } from 'src/interface/historical.interface';
import { AmendOrderParamsV5, CancelOrderParamsV5, OrderParamsV5, SetLeverageParamsV5 } from 'bybit-api';
import { Instrument } from 'src/interface/instrument.interface';
export declare class AdapterExternalService {
    private readonly logger;
    constructor(logger: Logger);
    switchToHedgeMode(): any;
    setLeverage(param: SetLeverageParamsV5): any;
    getWalletBalance(): Promise<number>;
    placeOrder(param: OrderParamsV5): any;
    amendOrder(param: AmendOrderParamsV5): any;
    cancelOrder(param: CancelOrderParamsV5): any;
    getActiveOrders(param: OrderParamsV5): any;
    getOrderHistory(param: OrderParamsV5): any;
    fetchBybitHistorical(param: GetHistoricalDto): Promise<Historical[]>;
    fetchBybitInstrument(param?: GetInstrumentDto): Promise<Instrument[]>;
}
