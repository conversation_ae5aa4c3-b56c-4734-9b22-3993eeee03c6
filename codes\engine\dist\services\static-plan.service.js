"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StaticPlanService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const log_detail_util_1 = require("../util/log-detail.util");
const instrument_service_1 = require("./instrument.service");
const get_entry_price_util_1 = require("../util/get-entry-price.util");
const get_target_price_util_1 = require("../util/get-target-price.util");
const get_expiry_date_util_1 = require("../util/get-expiry-date.util");
const pattern_service_1 = require("./pattern.service");
const convert_to_uuid_util_1 = require("../util/convert-to-uuid.util");
const historical_cache_service_1 = require("./historical-cache.service");
let StaticPlanService = class StaticPlanService {
    logger;
    instrumentService;
    historicalCacheService;
    patternService;
    constructor(logger, instrumentService, historicalCacheService, patternService) {
        this.logger = logger;
        this.instrumentService = instrumentService;
        this.historicalCacheService = historicalCacheService;
        this.patternService = patternService;
    }
    async getPlan(param, historical, patterns, instrument) {
        try {
            const historicalData = historical ?? (await this.historicalCacheService.getHistorical(param));
            const patternData = patterns ??
                (await this.patternService.getPattern(param, historicalData));
            param.entryPercentByClose =
                param.orderType === 'long'
                    ? -Math.abs(param.entryPercentByClose)
                    : Math.abs(param.entryPercentByClose);
            const diffFactor = 0.01;
            param.riskPercent =
                param.orderType === 'long'
                    ? -Math.abs(param.riskPercent) - diffFactor
                    : Math.abs(param.riskPercent) + diffFactor;
            param.rewardPercent =
                param.orderType === 'long'
                    ? Math.abs(param.rewardPercent)
                    : -Math.abs(param.rewardPercent);
            const planData = [];
            for (const item of patternData) {
                const id = (0, convert_to_uuid_util_1.convertToUUID)(`${param.methodId ?? ''}-${item.date.getTime()}`);
                const instrumentData = instrument ??
                    (await this.instrumentService.getInstrument({
                        symbol: item.symbol,
                    }));
                if (!instrumentData.length)
                    return [];
                const tickSize = instrumentData[0].priceFilter.tickSize;
                const entry = (0, get_entry_price_util_1.getEntry)({
                    orderType: param.orderType,
                    closePrice: item.close,
                    entryPercentByClose: param.entryPercentByClose,
                    tickSize,
                });
                const stopLoss = (0, get_target_price_util_1.getTargetPrice)({
                    mathRound: param.orderType === 'long' ? 'floor' : 'ceil',
                    entry,
                    targetPercent: param.riskPercent,
                    tickSize,
                });
                const takeProfit = (0, get_target_price_util_1.getTargetPrice)({
                    mathRound: param.orderType === 'long' ? 'floor' : 'ceil',
                    entry,
                    targetPercent: param.rewardPercent,
                    tickSize,
                });
                const expiryDate = (0, get_expiry_date_util_1.getExpiryDate)({
                    date: item.date,
                    interval: item.interval,
                    validityPeriod: param.validityPeriod,
                });
                planData.push({
                    id,
                    methodId: param.methodId ?? '',
                    orderType: param.orderType,
                    entry,
                    stopLoss,
                    takeProfit,
                    expiryDate,
                    entryPercentByClose: param.entryPercentByClose,
                    riskPercent: param.riskPercent,
                    rewardPercent: param.rewardPercent,
                    validityPeriod: param.validityPeriod,
                    stopPercent: ((stopLoss - entry) / entry) * 100,
                    profitPercent: ((takeProfit - entry) / entry) * 100,
                    date: item.date,
                    interval: item.interval,
                    symbol: item.symbol,
                    patternType: item.patternType,
                    pattern: item.pattern,
                });
            }
            return planData;
        }
        catch (error) {
            this.logger.error('Failed to fetch pattern data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getPlan',
                param,
                error,
            }));
            return [];
        }
    }
};
exports.StaticPlanService = StaticPlanService;
exports.StaticPlanService = StaticPlanService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        instrument_service_1.InstrumentService,
        historical_cache_service_1.HistoricalCacheService,
        pattern_service_1.PatternService])
], StaticPlanService);
//# sourceMappingURL=static-plan.service.js.map