{"version": 3, "file": "backtest-method.service.js", "sourceRoot": "", "sources": ["../../src/services/backtest-method.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AAEvD,qCAAiC;AAMjC,yDAAqD;AACrD,uDAAmD;AACnD,6DAAyD;AACzD,iEAA4D;AAC5D,iFAA2E;AAC3E,6DAAqD;AAErD,uEAAgD;AAEhD,+DAA0D;AAC1D,yEAAoE;AAG7D,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAEoB;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IARnB,YACoD,MAAc,EAC/C,sBAA8C,EAC9C,cAA8B,EAC9B,iBAAoC,EACpC,kBAAsC,EACtC,eAAgC,EAChC,yBAAoD,EACpD,iBAAoC;QAPH,WAAM,GAAN,MAAM,CAAQ;QAC/C,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,mBAAc,GAAd,cAAc,CAAgB;QAC9B,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,oBAAe,GAAf,eAAe,CAAiB;QAChC,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,sBAAiB,GAAjB,iBAAiB,CAAmB;IACpD,CAAC;IAEJ,KAAK,CAAC,uBAAuB,CAC3B,KAA2B,EAC3B,OAAwB,EACxB,UAAyB,EACzB,mBAAkC,EAClC,QAAoB,EACpB,UAAyB,EACzB,IAAa;QAEb,IAAI,CAAC;YACH,MAAM,cAAc,GAClB,UAAU,IAAI,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;YACzE,MAAM,uBAAuB,GAC3B,mBAAmB;gBACnB,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;oBAC/C,GAAG,KAAK;oBACR,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,QAAQ,EAAE,IAAA,wBAAc,EAAC,oBAAoB,CAAC;iBAC/C,CAAC,CAAC,CAAC;YACN,MAAM,WAAW,GACf,QAAQ;gBACR,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;YAChE,MAAM,cAAc,GAClB,UAAU;gBACV,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;oBAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;iBACrB,CAAC,CAAC,CAAC;YACN,MAAM,QAAQ,GACZ,IAAI;gBACJ,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS;oBAC7B,CAAC,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CACnC,KAAK,EACL,cAAc,EACd,WAAW,EACX,cAAc,CACf;oBACH,CAAC,CAAC,KAAK,CAAC,kBAAkB;wBACxB,CAAC,CAAC,MAAM,IAAI,CAAC,yBAAyB,CAAC,sBAAsB,CACzD,KAAK,EACL,cAAc,EACd,uBAAuB,EACvB,WAAW,EACX,cAAc,CACf;wBACH,CAAC,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAClC,KAAK,EACL,cAAc,EACd,WAAW,EACX,cAAc,CACf,CAAC,CAAC;YACX,MAAM,WAAW,GACf,OAAO;gBACP,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAC/C,QAAQ,EACR,uBAAuB,CACxB,CAAC,CAAC;YAEL,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,EAC3C,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,yBAAyB;gBACnC,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,4BAA4B,CAChC,KAA2B,EAC3B,OAAwB,EACxB,UAAyB,EACzB,mBAAkC,EAClC,QAAoB,EACpB,UAAyB,EACzB,IAAa;QAEb,IAAI,CAAC;YACH,MAAM,cAAc,GAClB,UAAU,IAAI,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;YACzE,MAAM,uBAAuB,GAC3B,mBAAmB;gBACnB,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;oBAC/C,GAAG,KAAK;oBACR,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,QAAQ,EAAE,IAAA,wBAAc,EAAC,oBAAoB,CAAC;iBAC/C,CAAC,CAAC,CAAC;YACN,MAAM,WAAW,GACf,QAAQ;gBACR,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;YAChE,MAAM,cAAc,GAClB,UAAU;gBACV,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;oBAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;iBACrB,CAAC,CAAC,CAAC;YACN,MAAM,QAAQ,GACZ,IAAI;gBACJ,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS;oBAC7B,CAAC,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CACnC,KAAK,EACL,cAAc,EACd,WAAW,EACX,cAAc,CACf;oBACH,CAAC,CAAC,KAAK,CAAC,kBAAkB;wBACxB,CAAC,CAAC,MAAM,IAAI,CAAC,yBAAyB,CAAC,sBAAsB,CACzD,KAAK,EACL,cAAc,EACd,uBAAuB,EACvB,WAAW,EACX,cAAc,CACf;wBACH,CAAC,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAClC,KAAK,EACL,cAAc,EACd,WAAW,EACX,cAAc,CACf,CAAC,CAAC;YACX,MAAM,WAAW,GACf,OAAO;gBACP,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAC/C,QAAQ,EACR,uBAAuB,CACxB,CAAC,CAAC;YACL,MAAM,eAAe,GACnB,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAEzD,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gDAAgD,EAChD,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,8BAA8B;gBACxC,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO;gBACL,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;gBACrB,OAAO,EAAE,IAAI,IAAI,EAAE;gBACnB,eAAe,EAAE,CAAC;gBAClB,iBAAiB,EAAE,CAAC;gBACpB,sBAAsB,EAAE,CAAC;gBACzB,WAAW,EAAE,CAAC;gBACd,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,CAAC;gBACjB,gBAAgB,EAAE,CAAC;gBACnB,kBAAkB,EAAE,CAAC;gBACrB,oBAAoB,EAAE,CAAC;gBACvB,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,CAAC;gBACjB,gBAAgB,EAAE,CAAC;gBACnB,kBAAkB,EAAE,CAAC;gBACrB,oBAAoB,EAAE,CAAC;gBACvB,gBAAgB,EAAE,CAAC;gBACnB,oBAAoB,EAAE,CAAC;gBACvB,oBAAoB,EAAE,CAAC;gBACvB,kBAAkB,EAAE,CAAC;gBACrB,iBAAiB,EAAE,CAAC;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,KAA2B,EAC3B,UAAyB,EACzB,mBAAkC,EAClC,QAAoB,EACpB,UAAyB,EACzB,IAAa,EACb,OAAwB;QAKxB,IAAI,CAAC;YACH,MAAM,cAAc,GAClB,UAAU,IAAI,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;YACzE,MAAM,uBAAuB,GAC3B,mBAAmB;gBACnB,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;oBAC/C,GAAG,KAAK;oBACR,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,QAAQ,EAAE,IAAA,wBAAc,EAAC,oBAAoB,CAAC;iBAC/C,CAAC,CAAC,CAAC;YACN,MAAM,WAAW,GACf,QAAQ;gBACR,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;YAChE,MAAM,cAAc,GAClB,UAAU;gBACV,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;oBAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;iBACrB,CAAC,CAAC,CAAC;YACN,MAAM,QAAQ,GACZ,IAAI;gBACJ,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS;oBAC7B,CAAC,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CACnC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE,EAAE,EAC5C,cAAc,EACd,WAAW,EACX,cAAc,CACf;oBACH,CAAC,CAAC,KAAK,CAAC,kBAAkB;wBACxB,CAAC,CAAC,MAAM,IAAI,CAAC,yBAAyB,CAAC,sBAAsB,CACzD,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE,EAAE,EAC5C,cAAc,EACd,uBAAuB,EACvB,WAAW,EACX,cAAc,CACf;wBACH,CAAC,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAClC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE,EAAE,EAC5C,cAAc,EACd,WAAW,EACX,cAAc,CACf,CAAC,CAAC;YACX,MAAM,WAAW,GACf,OAAO;gBACP,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAC/C,QAAQ,EACR,uBAAuB,CACxB,CAAC,CAAC;YAEL,MAAM,eAAe,GACnB,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YACzD,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yCAAyC,EACzC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,uBAAuB;gBACjC,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO;gBACL,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE;oBACX,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;oBACrB,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,eAAe,EAAE,CAAC;oBAClB,iBAAiB,EAAE,CAAC;oBACpB,sBAAsB,EAAE,CAAC;oBACzB,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,CAAC;oBACjB,gBAAgB,EAAE,CAAC;oBACnB,kBAAkB,EAAE,CAAC;oBACrB,oBAAoB,EAAE,CAAC;oBACvB,gBAAgB,EAAE,CAAC;oBACnB,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,CAAC;oBACjB,gBAAgB,EAAE,CAAC;oBACnB,kBAAkB,EAAE,CAAC;oBACrB,oBAAoB,EAAE,CAAC;oBACvB,gBAAgB,EAAE,CAAC;oBACnB,oBAAoB,EAAE,CAAC;oBACvB,oBAAoB,EAAE,CAAC;oBACvB,kBAAkB,EAAE,CAAC;oBACrB,iBAAiB,EAAE,CAAC;iBACrB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA7RY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;QACvB,iDAAsB;QAC9B,gCAAc;QACX,sCAAiB;QAChB,yCAAkB;QACrB,kCAAe;QACL,wDAAyB;QACjC,uCAAiB;GAT5C,qBAAqB,CA6RjC"}