"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrateToMonthlyCache = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class MigrateToMonthlyCache {
    getYearMonth(date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        return `${year}_${month}`;
    }
    parseCsvFile(filePath) {
        if (!fs.existsSync(filePath)) {
            return [];
        }
        try {
            const content = fs.readFileSync(filePath, 'utf-8').trim();
            if (!content)
                return [];
            const lines = content.split('\n');
            if (lines.length <= 1)
                return [];
            const validRecords = [];
            lines.slice(1).forEach((line) => {
                try {
                    const [date, symbol, interval, open, high, low, close, volume] = line.split(',');
                    if (!date || !symbol || !interval || !open || !high || !low || !close || !volume) {
                        return;
                    }
                    const parsedDate = new Date(date);
                    if (isNaN(parsedDate.getTime()))
                        return;
                    validRecords.push({
                        date: parsedDate,
                        symbol: symbol.trim(),
                        interval: interval.trim(),
                        open: parseFloat(open),
                        high: parseFloat(high),
                        low: parseFloat(low),
                        close: parseFloat(close),
                        volume: parseFloat(volume),
                    });
                }
                catch (err) {
                }
            });
            return validRecords;
        }
        catch (err) {
            console.error(`Failed to parse CSV file: ${filePath}`, err);
            return [];
        }
    }
    convertToCsv(data) {
        if (!data.length)
            return 'date,symbol,interval,open,high,low,close,volume\n';
        const header = 'date,symbol,interval,open,high,low,close,volume\n';
        const rows = data
            .map((item) => `${new Date(item.date).toISOString()},${item.symbol},${item.interval},${item.open},${item.high},${item.low},${item.close},${item.volume}`)
            .join('\n');
        return header + rows;
    }
    async migrateSymbol(symbol) {
        const symbolPath = path.join('data/historical', symbol);
        if (!fs.existsSync(symbolPath)) {
            console.log(`Symbol directory not found: ${symbolPath}`);
            return;
        }
        const files = fs.readdirSync(symbolPath).filter(file => file.endsWith('.csv'));
        for (const file of files) {
            const interval = file.replace('.csv', '');
            const oldFilePath = path.join(symbolPath, file);
            console.log(`Migrating ${symbol}/${interval}...`);
            const data = this.parseCsvFile(oldFilePath);
            if (data.length === 0) {
                console.log(`No data found in ${oldFilePath}`);
                continue;
            }
            const newDirPath = path.join(symbolPath, interval);
            fs.mkdirSync(newDirPath, { recursive: true });
            const monthlyData = new Map();
            data.forEach(item => {
                const yearMonth = this.getYearMonth(item.date);
                if (!monthlyData.has(yearMonth)) {
                    monthlyData.set(yearMonth, []);
                }
                monthlyData.get(yearMonth).push(item);
            });
            for (const [yearMonth, monthData] of monthlyData) {
                const monthFilePath = path.join(newDirPath, `${yearMonth}.csv`);
                const sortedData = monthData.sort((a, b) => a.date.getTime() - b.date.getTime());
                const csvContent = this.convertToCsv(sortedData);
                fs.writeFileSync(monthFilePath, csvContent, 'utf-8');
                console.log(`Created ${monthFilePath} with ${sortedData.length} records`);
            }
            const backupPath = `${oldFilePath}.backup.${Date.now()}`;
            fs.renameSync(oldFilePath, backupPath);
            console.log(`Backed up original file to ${backupPath}`);
        }
    }
    async migrateAll() {
        const historicalPath = 'data/historical';
        if (!fs.existsSync(historicalPath)) {
            console.log('Historical data directory not found');
            return;
        }
        const symbols = fs.readdirSync(historicalPath).filter(item => {
            const itemPath = path.join(historicalPath, item);
            return fs.statSync(itemPath).isDirectory();
        });
        console.log(`Found ${symbols.length} symbols to migrate`);
        for (const symbol of symbols) {
            try {
                await this.migrateSymbol(symbol);
                console.log(`✓ Completed migration for ${symbol}`);
            }
            catch (err) {
                console.error(`✗ Failed to migrate ${symbol}:`, err);
            }
        }
        console.log('Migration completed!');
    }
}
exports.MigrateToMonthlyCache = MigrateToMonthlyCache;
if (require.main === module) {
    const migrator = new MigrateToMonthlyCache();
    const args = process.argv.slice(2);
    if (args.length > 0) {
        migrator.migrateSymbol(args[0]).catch(console.error);
    }
    else {
        migrator.migrateAll().catch(console.error);
    }
}
//# sourceMappingURL=migrate-to-monthly-cache.js.map