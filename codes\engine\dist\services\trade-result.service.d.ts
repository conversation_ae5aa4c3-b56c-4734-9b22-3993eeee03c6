import { Logger } from 'winston';
import { TradeResult } from 'src/interface/trade-result.interface';
import { TradeResultEntity } from 'src/entity/trade-result.entity';
import { Repository } from 'typeorm';
export declare class TradeResultService {
    private readonly logger;
    private readonly tradeResultRepository;
    constructor(logger: Logger, tradeResultRepository: Repository<TradeResultEntity>);
    getByorderLinkId(orderLinkId: string): Promise<TradeResultEntity[]>;
    save(param: TradeResult): Promise<void>;
    getPendingResults(): Promise<TradeResult[]>;
    getTradeResultByOrderId(orderId: string): Promise<TradeResultEntity[]>;
    getTradeResultByPrice(param: {
        price: string;
        side: string;
        stopLoss: string;
        takeProfit: string;
        symbol: string;
    }): Promise<TradeResultEntity[]>;
    getActiveResults(): Promise<TradeResult[]>;
    private checkOrderIdColumnExists;
    getClosedResults(limit: number): Promise<TradeResult[]>;
    updateStatus(param: TradeResult): Promise<void>;
}
