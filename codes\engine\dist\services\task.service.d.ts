import { OnModuleInit } from '@nestjs/common';
import { HistoricalIngestionService } from './historical-ingestion.service';
import { InstrumentIngestionService } from './instrument-ingestion.service';
import { MethodStatusService } from './method-status.service';
import { MethodUpdaterService } from './method-updater.service';
import { MethodIngestionService } from './method-ingestion.service';
import { InstrumentService } from './instrument.service';
import { TraderExecutorService } from './trade-executor.service';
import { TradeResultUpdaterService } from './trade-result-updater.service';
export declare class TaskService implements OnModuleInit {
    private readonly historicalIngestionService;
    private readonly methodStatusService;
    private readonly instrumentIngestionService;
    private readonly methodUpdaterService;
    private readonly methodIngestionService;
    private readonly instrumentService;
    private readonly traderExecutorService;
    private readonly tradeResultUpdaterService;
    private readonly logger;
    private isEngineRunning;
    constructor(historicalIngestionService: HistoricalIngestionService, methodStatusService: MethodStatusService, instrumentIngestionService: InstrumentIngestionService, methodUpdaterService: MethodUpdaterService, methodIngestionService: MethodIngestionService, instrumentService: InstrumentService, traderExecutorService: TraderExecutorService, tradeResultUpdaterService: TradeResultUpdaterService);
    onModuleInit(): Promise<void>;
    handleInstrumentStatus(): Promise<void>;
    handleInterval(): Promise<void>;
}
