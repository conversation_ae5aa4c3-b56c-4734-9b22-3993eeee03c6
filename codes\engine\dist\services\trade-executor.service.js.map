{"version": 3, "file": "trade-executor.service.js", "sourceRoot": "", "sources": ["../../src/services/trade-executor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AACvD,6DAAqD;AACrD,qCAAiC;AACjC,mFAA6E;AAC7E,uEAAgD;AAChD,qFAA4E;AAC5E,6DAAyD;AAIzD,uDAAmD;AACnD,uEAA8D;AAC9D,2EAAkE;AAClE,iEAA4D;AAE5D,+BAAoC;AACpC,mDAA4C;AAC5C,qDAAiD;AACjD,iFAAwE;AACxE,qFAA2E;AAC3E,yEAAoE;AACpE,iEAA0D;AAGnD,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAEoB;IACjC;IACA;IACA;IACA;IACA;IACA;IAPnB,YACoD,MAAc,EAC/C,0BAAsD,EACtD,iBAAoC,EACpC,cAA8B,EAC9B,kBAAsC,EACtC,aAA4B,EAC5B,sBAA8C;QANb,WAAM,GAAN,MAAM,CAAQ;QAC/C,+BAA0B,GAA1B,0BAA0B,CAA4B;QACtD,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,mBAAc,GAAd,cAAc,CAAgB;QAC9B,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,kBAAa,GAAb,aAAa,CAAe;QAC5B,2BAAsB,GAAtB,sBAAsB,CAAwB;IAC9D,CAAC;IAEJ,KAAK,CAAC,GAAG;QACP,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAA,wBAAc,EAAC,uBAAuB,CAAC,CAAC;YAC/D,MAAM,WAAW,GAAG,IAAA,wBAAc,EAAC,cAAc,CAAC,CAAC;YAEnD,MAAM,QAAQ,GACZ,MAAM,IAAI,CAAC,0BAA0B,CAAC,0BAA0B,CAAC;gBAC/D,cAAc;gBACd,WAAW;gBACX,iBAAiB,EAAE,IAAI;aACxB,CAAC,CAAC;YACL,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACnE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACjE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC;gBACtC,OAAO;gBACP,WAAW;aACZ,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC7B,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,eAAe,EACf,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,KAAK;gBACf,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO;QACT,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,OAAuB;QAC1D,MAAM,cAAc,GAAmB,EAAE,CAAC;QAC1C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAE7B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;gBAC5D,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,UAAU;gBAAE,SAAS;YAC1B,MAAM,eAAe,GACnB,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBACxC,IAAA,oCAAa,EAAC,IAAA,wBAAc,EAAC,oBAAoB,CAAC,CAAC,CAAC;YAEtD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,eAAe,CACjE,MAAM,CAAC,MAAM,EACb,IAAA,wBAAc,EAAC,oBAAoB,CAAC,CACrC,CAAC;YACF,IAAI,SAAS,GAAG,eAAe;gBAAE,SAAS;YAI1C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAC/B,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAC5C,CAAC;YACF,IAAI,QAAQ;gBAAE,SAAS;YAGvB,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,MAAM;QAClB,MAAM,aAAa,GACjB,IAAA,wBAAc,EAAC,gBAAgB,CAAC,IAAI,IAAA,wBAAc,EAAC,YAAY,CAAC,CAAC;QACnE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC;YAC1D,KAAK,MAAM,MAAM,IAAI,IAAA,gCAAY,EAAC,OAAO,CAAC,EAAE,CAAC;gBAC3C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC;oBAC7D,QAAQ,EAAE,QAAQ;oBAClB,MAAM;iBACP,CAAC,CAAC;gBACH,IAAI,CAAC,YAAY;oBAAE,OAAO;gBAC1B,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;oBACvC,IAAI,WAAW,CAAC,KAAK,KAAK,GAAG;wBAAE,SAAS;oBACxC,MAAM,YAAY,GAChB,MAAM,IAAI,CAAC,kBAAkB,CAAC,uBAAuB,CACnD,WAAW,CAAC,OAAO,CACpB,CAAC;oBACJ,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;wBACzB,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;4BACpC,QAAQ,EAAE,QAAQ;4BAClB,MAAM,EAAE,WAAW,CAAC,MAAM;4BAC1B,OAAO,EAAE,WAAW,CAAC,OAAO;yBAC7B,CAAC,CAAC;wBACH,SAAS;oBACX,CAAC;oBACD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;wBACvC,IACE,WAAW,CAAC,MAAM,KAAK,SAAS;4BAChC,WAAW,CAAC,MAAM,KAAK,MAAM;4BAE7B,SAAS;wBACX,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;4BACpC,QAAQ,EAAE,QAAQ;4BAClB,MAAM,EAAE,WAAW,CAAC,MAAM;4BAC1B,OAAO,EAAE,WAAW,CAAC,OAAO;yBAC7B,CAAC,CAAC;wBAEH,MAAM,IAAA,kBAAK,EAAC,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,oBAAoB;gBAC3B,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,UAAyB;QAC3C,IAAI,CAAC;YACH,MAAM,aAAa,GACjB,IAAA,wBAAc,EAAC,gBAAgB,CAAC,IAAI,IAAA,wBAAc,EAAC,YAAY,CAAC,CAAC;YACnE,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC;oBAC/D,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,QAAQ,EAAE,GAAG;oBACb,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAA,oCAAa,EAAC,GAAG,CAAC,CAAC;oBAChD,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,IAAI,EAAE,MAAM;iBACb,CAAC,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACvD,SAAS,CAAC,MAAM,CACjB,CAAC;gBAEF,MAAM,gBAAgB,GAAgB;oBACpC,GAAG,SAAS;oBACZ,GAAG,IAAA,wCAAe,EAAC;wBACjB,IAAI,EAAE,SAAS,CAAC,IAAI;wBACpB,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE;wBACnC,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI,GAAG;wBAC7B,QAAQ,EAAE,QAAQ;qBACnB,CAAC;iBACH,CAAC;gBACF,MAAM,gBAAgB,GACpB,MAAM,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;gBAErD,MAAM,KAAK,GAAkB,IAAA,8CAAkB,EAAC,gBAAgB,CAAC,CAAC;gBAClE,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;oBACpC,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,WAAW,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;oBACxC,YAAY,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;iBAC1C,CAAC,CAAC;gBAEH,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;gBAC9C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC;oBAC7D,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,SAAS,CAAC,MAAM;iBACzB,CAAC,CAAC;gBAEH,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;oBACxB,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;wBACjC,IACE,KAAK,CAAC,KAAK,KAAK,gBAAgB,CAAC,KAAK;4BACtC,KAAK,CAAC,UAAU,KAAK,gBAAgB,CAAC,UAAU;4BAChD,KAAK,CAAC,QAAQ,KAAK,gBAAgB,CAAC,QAAQ;4BAC5C,KAAK,CAAC,IAAI,KAAK,gBAAgB,CAAC,IAAI,EACpC,CAAC;4BACD,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;4BACzC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;gCACnC,GAAG,gBAAgB;gCACnB,OAAO,EAAE,KAAK,CAAC,OAAO;6BACvB,CAAC,CAAC;4BACH,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACvD,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;oBAC7D,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;wBACxC,gBAAgB,CAAC,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;wBACnD,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBACvD,CAAC;gBACH,CAAC;gBACD,MAAM,IAAA,kBAAK,EAAC,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uBAAuB,EACvB,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,OAAO;gBACjB,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,KAGvB;QACC,IAAI,CAAC;YACH,MAAM,UAAU,GAAkB,EAAE,CAAC;YAErC,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBACnC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;oBACpD,QAAQ,EAAE,MAAM,CAAC,QAAQ;iBAC1B,CAAC,CAAC;gBACH,IAAI,CAAC,WAAW,CAAC,MAAM;oBAAE,SAAS;gBAClC,MAAM,WAAW,GAAG,IAAA,iDAAmB,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxD,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC1D,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAChD,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,MAAM;wBAC3B,CAAC,CAAC,CAAC;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC;gBACN,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC;oBAClC,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,UAAU,EAAE,MAAM,CAAC,KAAK;oBACxB,MAAM,EAAE,MAAM,CAAC,MAAM;iBACtB,CAAC,CAAC;gBAEH,MAAM,IAAI,GAAgB;oBACxB,EAAE,EAAE,MAAM,CAAC,EAAE,IAAI,IAAA,SAAM,GAAE;oBACzB,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,UAAU,EAAE,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,OAAO;oBAClB,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE;oBACnB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;oBAC9B,gBAAgB,EAAE,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrD,YAAY,EAAE,EAAE;oBAChB,SAAS,EAAE,WAAW;oBACtB,WAAW;oBACX,WAAW;oBACX,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE;oBACxC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBACpC,WAAW,EAAE,WAAW;oBACxB,WAAW,EAAE,WAAW;oBACxB,QAAQ,EAAE,SAAS;oBACnB,WAAW,EAAE,QAAQ;oBACrB,WAAW,EAAE,QAAQ;oBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,WAAW,EAAE,MAAM,CAAC,WAAW;oBAC/B,aAAa,EAAE,MAAM,CAAC,aAAa;oBACnC,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,UAAU,EAAE,MAAM,CAAC,UAAU;iBAC9B,CAAC;gBACF,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;YACD,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sBAAsB,EACtB,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,WAAW;gBACrB,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,WAAqC;QAErC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;QAC5E,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;QACxE,MAAM,kBAAkB,GACtB,MAAM,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,CAAC;QAEvD,OAAO,IAAA,kDAAoB,EAAC;YAC1B,gBAAgB;YAChB,eAAe,EAAE,WAAW,CAAC,eAAe;YAC5C,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;YAClD,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;YAC9C,kBAAkB;YAClB,cAAc;SACf,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAI1B;QACC,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;YACnE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACtE,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC;YACjE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAC7D,KAAK,CAAC,MAAM,CACb,CAAC;YACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAC7D,KAAK,CAAC,MAAM,CACb,CAAC;YACF,MAAM,MAAM,GAAG,CAAC,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;YACtE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;YAC/C,MAAM,GAAG,GAAG,SAAS,GAAG,OAAO,CAAC;YAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CACvB,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,EAC1B,WAAW,CACZ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACtB,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yBAAyB,EACzB,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,cAAc;gBACxB,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;CACF,CAAA;AArVY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;QACnB,0DAA0B;QACnC,sCAAiB;QACpB,gCAAc;QACV,yCAAkB;QACvB,8BAAa;QACJ,iDAAsB;GARtD,qBAAqB,CAqVjC"}