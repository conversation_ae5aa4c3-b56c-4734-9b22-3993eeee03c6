"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BacktestMethodService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const backtest_service_1 = require("./backtest.service");
const pattern_service_1 = require("./pattern.service");
const instrument_service_1 = require("./instrument.service");
const dynamic_plan_service_1 = require("./dynamic-plan.service");
const optimize_static_plan_service_1 = require("./optimize-static-plan.service");
const log_detail_util_1 = require("../util/log-detail.util");
const configurations_1 = __importDefault(require("../configurations"));
const static_plan_service_1 = require("./static-plan.service");
const historical_cache_service_1 = require("./historical-cache.service");
let BacktestMethodService = class BacktestMethodService {
    logger;
    historicalCacheService;
    patternService;
    instrumentService;
    dynamicPlanService;
    backtestService;
    optimizeStaticPlanService;
    staticPlanService;
    constructor(logger, historicalCacheService, patternService, instrumentService, dynamicPlanService, backtestService, optimizeStaticPlanService, staticPlanService) {
        this.logger = logger;
        this.historicalCacheService = historicalCacheService;
        this.patternService = patternService;
        this.instrumentService = instrumentService;
        this.dynamicPlanService = dynamicPlanService;
        this.backtestService = backtestService;
        this.optimizeStaticPlanService = optimizeStaticPlanService;
        this.staticPlanService = staticPlanService;
    }
    async getBacktestMethodResult(param, results, historical, historicalExecution, patterns, instrument, plan) {
        try {
            const historicalData = historical ?? (await this.historicalCacheService.getHistorical(param));
            const historicalExecutionData = historicalExecution ??
                (await this.historicalCacheService.getHistorical({
                    ...param,
                    end: new Date(),
                    interval: (0, configurations_1.default)('EXECUTION_INTERVAL'),
                }));
            const patternData = patterns ??
                (await this.patternService.getPattern(param, historicalData));
            const instrumentData = instrument ??
                (await this.instrumentService.getInstrument({
                    symbol: param.symbol,
                }));
            const planData = plan ??
                (param.methodType === 'dynamic'
                    ? await this.dynamicPlanService.getPlan(param, historicalData, patternData, instrumentData)
                    : param.enableOptimization
                        ? await this.optimizeStaticPlanService.getOptimizedStaticPlan(param, historicalData, historicalExecutionData, patternData, instrumentData)
                        : await this.staticPlanService.getPlan(param, historicalData, patternData, instrumentData));
            const resultsData = results ??
                (await this.backtestService.getSingleSymbolResult(planData, historicalExecutionData));
            return resultsData;
        }
        catch (error) {
            this.logger.error('Failed to generate backtest method result', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMethodService',
                function: 'getBacktestMethodResult',
                param,
                error,
            }));
            return [];
        }
    }
    async getBacktestMethodPerformance(param, results, historical, historicalExecution, patterns, instrument, plan) {
        try {
            const historicalData = historical ?? (await this.historicalCacheService.getHistorical(param));
            const historicalExecutionData = historicalExecution ??
                (await this.historicalCacheService.getHistorical({
                    ...param,
                    end: new Date(),
                    interval: (0, configurations_1.default)('EXECUTION_INTERVAL'),
                }));
            const patternData = patterns ??
                (await this.patternService.getPattern(param, historicalData));
            const instrumentData = instrument ??
                (await this.instrumentService.getInstrument({
                    symbol: param.symbol,
                }));
            const planData = plan ??
                (param.methodType === 'dynamic'
                    ? await this.dynamicPlanService.getPlan(param, historicalData, patternData, instrumentData)
                    : param.enableOptimization
                        ? await this.optimizeStaticPlanService.getOptimizedStaticPlan(param, historicalData, historicalExecutionData, patternData, instrumentData)
                        : await this.staticPlanService.getPlan(param, historicalData, patternData, instrumentData));
            const resultsData = results ??
                (await this.backtestService.getSingleSymbolResult(planData, historicalExecutionData));
            const performanceData = await this.backtestService.getPerformance(resultsData);
            return performanceData;
        }
        catch (error) {
            this.logger.error('Failed to generate backtest method performance', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMethodService',
                function: 'getBacktestMethodPerformance',
                param,
                error,
            }));
            return {
                methodId: '',
                fromDate: new Date(0),
                endDate: new Date(),
                totalValidTrade: 0,
                totalInvalidTrade: 0,
                averageRewardRiskRatio: 0,
                probability: 0,
                avgOpenPosition: 0,
                avgStopPercent: 0,
                avgProfitPercent: 0,
                avgConsecutiveLoss: 0,
                avgConsecutiveProfit: 0,
                avgHoldingPeriod: 0,
                maxOpenPosition: 0,
                maxStopPercent: 0,
                maxProfitPercent: 0,
                maxConsecutiveLoss: 0,
                maxConsecutiveProfit: 0,
                maxHoldingPeriod: 0,
                cumulativePercentage: 0,
                avgValidTradeByMonth: 0,
                avgValidTradeByDay: 0,
                avgRevenueByTrade: 0,
            };
        }
    }
    async getBacktestMethodBoth(param, historical, historicalExecution, patterns, instrument, plan, results) {
        try {
            const historicalData = historical ?? (await this.historicalCacheService.getHistorical(param));
            const historicalExecutionData = historicalExecution ??
                (await this.historicalCacheService.getHistorical({
                    ...param,
                    end: new Date(),
                    interval: (0, configurations_1.default)('EXECUTION_INTERVAL'),
                }));
            const patternData = patterns ??
                (await this.patternService.getPattern(param, historicalData));
            const instrumentData = instrument ??
                (await this.instrumentService.getInstrument({
                    symbol: param.symbol,
                }));
            const planData = plan ??
                (param.methodType === 'dynamic'
                    ? await this.dynamicPlanService.getPlan({ ...param, methodId: param.methodId ?? '' }, historicalData, patternData, instrumentData)
                    : param.enableOptimization
                        ? await this.optimizeStaticPlanService.getOptimizedStaticPlan({ ...param, methodId: param.methodId ?? '' }, historicalData, historicalExecutionData, patternData, instrumentData)
                        : await this.staticPlanService.getPlan({ ...param, methodId: param.methodId ?? '' }, historicalData, patternData, instrumentData));
            const resultsData = results ??
                (await this.backtestService.getSingleSymbolResult(planData, historicalExecutionData));
            const performanceData = await this.backtestService.getPerformance(resultsData);
            return { result: resultsData, performance: performanceData };
        }
        catch (error) {
            this.logger.error('Failed to generate backtest method both', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMethodService',
                function: 'getBacktestMethodBoth',
                param,
                error,
            }));
            return {
                result: [],
                performance: {
                    methodId: '',
                    fromDate: new Date(0),
                    endDate: new Date(),
                    totalValidTrade: 0,
                    totalInvalidTrade: 0,
                    averageRewardRiskRatio: 0,
                    probability: 0,
                    avgOpenPosition: 0,
                    avgStopPercent: 0,
                    avgProfitPercent: 0,
                    avgConsecutiveLoss: 0,
                    avgConsecutiveProfit: 0,
                    avgHoldingPeriod: 0,
                    maxOpenPosition: 0,
                    maxStopPercent: 0,
                    maxProfitPercent: 0,
                    maxConsecutiveLoss: 0,
                    maxConsecutiveProfit: 0,
                    maxHoldingPeriod: 0,
                    cumulativePercentage: 0,
                    avgValidTradeByMonth: 0,
                    avgValidTradeByDay: 0,
                    avgRevenueByTrade: 0,
                },
            };
        }
    }
};
exports.BacktestMethodService = BacktestMethodService;
exports.BacktestMethodService = BacktestMethodService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        historical_cache_service_1.HistoricalCacheService,
        pattern_service_1.PatternService,
        instrument_service_1.InstrumentService,
        dynamic_plan_service_1.DynamicPlanService,
        backtest_service_1.BacktestService,
        optimize_static_plan_service_1.OptimizeStaticPlanService,
        static_plan_service_1.StaticPlanService])
], BacktestMethodService);
//# sourceMappingURL=backtest-method.service.js.map