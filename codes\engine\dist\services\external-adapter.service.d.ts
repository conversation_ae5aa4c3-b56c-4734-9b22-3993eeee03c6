import { Logger } from 'winston';
export declare class ExternalAdapterService {
    private readonly logger;
    private readonly httpClient;
    private readonly baseUrl;
    private readonly apiKey;
    constructor(logger: Logger);
    switchToHedgeMode(param: any): Promise<any>;
    setLeverage(param: any): Promise<any>;
    getWalletBalance(): Promise<number>;
    placeOrder(param: any): Promise<any>;
    amendOrder(param: any): Promise<any>;
    cancelOrder(param: any): Promise<any>;
    getActiveOrders(param: any): Promise<any[]>;
    getOrderHistory(param: any): Promise<any[]>;
    getHistorical(param: any): Promise<any[]>;
    getInstrument(param: any): Promise<any[]>;
}
