"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertToUUID = convertToUUID;
const configurations_1 = __importDefault(require("../configurations"));
const uuid_1 = require("uuid");
function convertToUUID(param) {
    return (0, uuid_1.v5)(param, (0, configurations_1.default)('UUID_NAMESPACE'));
}
//# sourceMappingURL=convert-to-uuid.util.js.map