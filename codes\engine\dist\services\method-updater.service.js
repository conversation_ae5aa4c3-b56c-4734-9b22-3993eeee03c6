"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MethodUpdaterService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const method_service_1 = require("./method.service");
const method_status_service_1 = require("./method-status.service");
const backtest_method_service_1 = require("./backtest-method.service");
const is_good_method_util_1 = require("../util/is-good-method.util");
const configurations_1 = __importDefault(require("../configurations"));
const historical_cache_service_1 = require("./historical-cache.service");
const to_milliseconds_util_1 = require("../util/to-milliseconds.util");
const instrument_service_1 = require("./instrument.service");
let MethodUpdaterService = class MethodUpdaterService {
    logger;
    methodStatusService;
    methodService;
    backtestMethodService;
    historicalCacheService;
    instrumentService;
    constructor(logger, methodStatusService, methodService, backtestMethodService, historicalCacheService, instrumentService) {
        this.logger = logger;
        this.methodStatusService = methodStatusService;
        this.methodService = methodService;
        this.backtestMethodService = backtestMethodService;
        this.historicalCacheService = historicalCacheService;
        this.instrumentService = instrumentService;
    }
    async updateMethod(end) {
        try {
            await this.syncHelper();
            for (let index = 0; index < 2; index++) {
                const methodStatuses = await this.methodStatusService.getMethodStatus();
                if (!methodStatuses || methodStatuses.length === 0)
                    break;
                else
                    index = 0;
                const intervals = [
                    ...new Set(methodStatuses.map((item) => item.interval)),
                ];
                const symbols = [...new Set(methodStatuses.map((item) => item.symbol))];
                for (const symbol of symbols) {
                    const instrument = await this.instrumentService.getInstrument({
                        symbol,
                    });
                    if (!instrument)
                        return;
                    const listedTime = new Date(Number(instrument[0].listedTime)).getTime();
                    const historicalExecutionLengthTarget = Math.ceil((end.getTime() - listedTime) /
                        (0, to_milliseconds_util_1.toMiliseconds)((0, configurations_1.default)('EXECUTION_INTERVAL')));
                    const historicalExecutionLength = await this.historicalCacheService.countHistorical(symbol, (0, configurations_1.default)('EXECUTION_INTERVAL'));
                    if (historicalExecutionLength < historicalExecutionLengthTarget)
                        continue;
                    const historicalExecutionData = await this.historicalCacheService.getHistorical({
                        symbol,
                        interval: (0, configurations_1.default)('EXECUTION_INTERVAL'),
                        start: new Date(0),
                        end,
                        limit: (0, configurations_1.default)('HISTORICAL_EXECUTION_LIMIT'),
                        sort: 'ASC',
                    });
                    for (const interval of intervals) {
                        for (const methodStatus of methodStatuses.filter((item) => item.symbol === symbol && item.interval === interval)) {
                            const param = await this.methodService.getParam(methodStatus);
                            if (!param) {
                                await this.methodStatusService.deleteMethodStatus(methodStatus.methodId);
                                await this.methodService.deleteMethodResult(methodStatus.methodId);
                                await this.methodService.deleteMethodPerformance(methodStatus.methodId);
                                await this.methodStatusService.deleteMethodStatus(methodStatus.methodId);
                                continue;
                            }
                            const historicalLengthTarget = Math.ceil((end.getTime() - listedTime) / (0, to_milliseconds_util_1.toMiliseconds)(interval));
                            const historicalLength = await this.historicalCacheService.countHistorical(symbol, interval);
                            if (historicalLength < historicalLengthTarget)
                                continue;
                            const historicalData = await this.historicalCacheService.getHistorical({
                                symbol,
                                interval,
                                start: new Date(0),
                                end,
                                limit: (0, configurations_1.default)('HISTORICAL_EXECUTION_LIMIT'),
                                sort: 'ASC',
                            });
                            const methodParam = param[0];
                            const { result, performance } = await this.backtestMethodService.getBacktestMethodBoth(methodParam, historicalData, historicalExecutionData);
                            if ((0, is_good_method_util_1.isGoodMethod)(result, performance)) {
                                await this.methodService.insertMethodParam(methodParam);
                                await this.methodService.insertMethodResult(result);
                                await this.methodService.insertMethodPerformance(performance);
                                await this.methodStatusService.updateMethodStatus({
                                    symbol: methodStatus.symbol,
                                    interval: methodStatus.interval,
                                    methodId: methodStatus.methodId,
                                    updatedAt: new Date(),
                                    isUpdated: true,
                                });
                            }
                            else {
                                await this.methodService.deleteMethodParam(methodStatus.methodId);
                                await this.methodService.deleteMethodResult(methodStatus.methodId);
                                await this.methodService.deleteMethodPerformance(methodStatus.methodId);
                                await this.methodStatusService.deleteMethodStatus(methodStatus.methodId);
                            }
                        }
                    }
                }
            }
            return;
        }
        catch (err) {
            this.logger.error('Failed to update method data', (0, log_detail_util_1.logDetail)({
                class: 'MethodUpdaterService',
                function: 'updateMethod',
                error: err,
            }));
            return;
        }
    }
    async syncHelper() {
        try {
            const methodIds = await this.methodService.getMethodIds();
            const methodIdsString = methodIds.map((item) => item.methodId);
            await this.methodService.deleteMethodResultNotIn(methodIdsString);
            await this.methodService.deleteMethodPerformanceNotIn(methodIdsString);
        }
        catch (err) {
            this.logger.error('Failed to sync method data', (0, log_detail_util_1.logDetail)({
                class: 'MethodUpdaterService',
                function: 'syncHelper',
                error: err,
            }));
            return;
        }
    }
};
exports.MethodUpdaterService = MethodUpdaterService;
exports.MethodUpdaterService = MethodUpdaterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        method_status_service_1.MethodStatusService,
        method_service_1.MethodService,
        backtest_method_service_1.BacktestMethodService,
        historical_cache_service_1.HistoricalCacheService,
        instrument_service_1.InstrumentService])
], MethodUpdaterService);
//# sourceMappingURL=method-updater.service.js.map