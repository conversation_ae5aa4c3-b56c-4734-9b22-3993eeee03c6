"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateorderLinkId = generateorderLinkId;
const convert_to_uuid_util_1 = require("./convert-to-uuid.util");
function generateorderLinkId(param) {
    const key = `${param.symbol}-${param.interval}-${param.methodType}-${param.orderType}-${param.patternType}-${param.pattern}-${param.trend}-${param.enableOptimization}`;
    return (0, convert_to_uuid_util_1.convertToUUID)(key);
}
//# sourceMappingURL=generate-order-link-id.util.js.map