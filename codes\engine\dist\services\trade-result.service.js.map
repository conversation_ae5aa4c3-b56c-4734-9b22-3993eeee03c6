{"version": 3, "file": "trade-result.service.js", "sourceRoot": "", "sources": ["../../src/services/trade-result.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AACvD,6DAAqD;AACrD,qCAAiC;AAEjC,uEAAmE;AACnE,qCAAqC;AACrC,6CAAmD;AAG5C,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAEuB;IAEjC;IAHnB,YACoD,MAAc,EAE/C,qBAAoD;QAFnB,WAAM,GAAN,MAAM,CAAQ;QAE/C,0BAAqB,GAArB,qBAAqB,CAA+B;IACpE,CAAC;IAEJ,KAAK,CAAC,gBAAgB,CAAC,WAAmB;QACxC,IAAI,CAAC;YACH,MAAM,YAAY,GAChB,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEhE,OAAO,MAAM,YAAY;iBACtB,KAAK,CAAC,yCAAyC,EAAE,EAAE,WAAW,EAAE,CAAC;iBACjE,OAAO,EAAE,CAAC;QACf,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,EAClC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,kBAAkB;gBAC5B,KAAK,EAAE,GAAG;aACX,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,KAAkB;QAC3B,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kBAAkB,EAClB,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,MAAM;gBAChB,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO;QACT,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC;YACH,MAAM,YAAY,GAChB,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEhE,YAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE;gBACrD,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YACH,OAAO,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,EAClC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,mBAAmB;gBAC7B,KAAK,EAAE,GAAG;aACX,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,OAAe;QAC3C,IAAI,CAAC;YACH,MAAM,YAAY,GAChB,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEhE,OAAO,MAAM,YAAY;iBACtB,KAAK,CAAC,iCAAiC,EAAE,EAAE,OAAO,EAAE,CAAC;iBACrD,OAAO,EAAE,CAAC;QACf,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,EAClC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,yBAAyB;gBACnC,KAAK,EAAE,GAAG;aACX,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,KAM3B;QACC,IAAI,CAAC;YACH,MAAM,YAAY,GAChB,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEhE,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE;gBACnD,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,YAAY,CAAC,QAAQ,CAAC,2BAA2B,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YACzE,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE;gBACzD,QAAQ,EAAE,KAAK,CAAC,QAAQ;aACzB,CAAC,CAAC;YACH,YAAY,CAAC,QAAQ,CAAC,uCAAuC,EAAE;gBAC7D,UAAU,EAAE,KAAK,CAAC,UAAU;aAC7B,CAAC,CAAC;YACH,YAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE;gBACrD,MAAM,EAAE,KAAK,CAAC,MAAM;aACrB,CAAC,CAAC;YACH,OAAO,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,EAClC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,uBAAuB;gBACjC,KAAK,EAAE,GAAG;aACX,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAE3D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,2EAA2E,EAC3E,IAAA,2BAAS,EAAC;oBACR,KAAK,EAAE,oBAAoB;oBAC3B,QAAQ,EAAE,kBAAkB;iBAC7B,CAAC,CACH,CAAC;gBAGF,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;oBAC3C,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;iBACnD,CAAC,CAAC;YACL,CAAC;YAED,MAAM,YAAY,GAChB,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEhE,YAAY,CAAC,QAAQ,CACnB,kEAAkE,EAClE;gBACE,OAAO,EAAE,MAAM;gBACf,OAAO,EAAE,SAAS;aACnB,CACF,CAAC;YACF,OAAO,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,EAClC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,kBAAkB;gBAC5B,KAAK,EAAE,GAAG;aACX,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,IAAI,CAAC;YACH,MAAM,WAAW,GACf,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;OAKtC,CAAC,CAAC;YACH,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,0CAA0C,EAC1C,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,0BAA0B;gBACpC,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,KAAa;QAClC,IAAI,CAAC;YACH,MAAM,YAAY,GAChB,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEhE,YAAY,CAAC,QAAQ,CACnB,qEAAqE,EACrE;gBACE,OAAO,EAAE,SAAS;gBAClB,OAAO,EAAE,MAAM;aAChB,CACF,CAAC;YAEF,YAAY,CAAC,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;YACvD,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,OAAO,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,EAClC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,kBAAkB;gBAC5B,KAAK,EAAE,GAAG;aACX,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAkB;QACnC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CACrC,EAAE,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,EAClC,EAAE,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kBAAkB,EAClB,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,cAAc;gBACxB,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO;QACT,CAAC;QACD,OAAO;IACT,CAAC;CACF,CAAA;AAnPY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;IAC/B,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCADsB,gBAAM;QAExB,oBAAU;GAJzC,kBAAkB,CAmP9B"}