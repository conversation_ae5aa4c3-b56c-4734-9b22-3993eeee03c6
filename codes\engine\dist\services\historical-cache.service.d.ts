import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { Historical } from 'src/interface/historical.interface';
import { Logger } from 'winston';
export declare class HistoricalCacheService {
    private readonly logger;
    constructor(logger: Logger);
    private getYearMonth;
    private getMonthlyFilePaths;
    private writeWithRetry;
    private convertToCsv;
    private parseCsvFile;
    validateCache(symbol: string, interval: string, yearMonth?: string): Promise<boolean>;
    getHistorical(param: GetHistoricalDto): Promise<Historical[]>;
    insertHistorical(param: Historical[]): Promise<void>;
    deleteHistorical(param: {
        symbol: string;
        interval: string;
        yearMonth?: string;
    }): Promise<void>;
    countHistorical(symbol: string, interval: string, start?: Date, end?: Date): Promise<number>;
    getAvailableMonths(symbol: string, interval: string): Promise<string[]>;
    getCacheInfo(symbol: string, interval: string): Promise<{
        totalMonths: number;
        months: string[];
        totalRecords: number;
        oldestRecord?: Date;
        newestRecord?: Date;
    }>;
    cleanupEmptyMonths(symbol: string, interval: string): Promise<number>;
    cleanupOldBackups(maxAgeDays?: number): Promise<void>;
}
