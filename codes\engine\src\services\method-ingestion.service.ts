import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import configurations from 'src/configurations';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import { BacktestMethodService } from './backtest-method.service';
import { MethodService } from './method.service';
import { InstrumentService } from './instrument.service';
import { PatternService } from './pattern.service';
import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { Historical } from 'src/interface/historical.interface';
import { Pattern } from 'src/interface/pattern.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { v5 as uuidv5 } from 'uuid';
import { MethodStatusService } from './method-status.service';
import { OptimizeStaticPlanService } from './optimize-static-plan.service';
import { OptimizedStaticPlan } from 'src/interface/optimized-static-plan.interface';
import { isGoodMethod } from 'src/util/is-good-method.util';
import { HistoricalCacheService } from './historical-cache.service';
import { shuffleArray } from 'src/util/suffle-array.util';

@Injectable()
export class MethodIngestionService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly backtestMethodService: BacktestMethodService,
    private readonly methodService: MethodService,
    private readonly instrumentService: InstrumentService,
    private readonly patternService: PatternService,
    private readonly historicalCacheService: HistoricalCacheService,
    private readonly optimizeStaticPlanService: OptimizeStaticPlanService,
    private readonly methodStatusService: MethodStatusService,
  ) {}

  async ingestForever(symbols: string[]) {
    while (true) {
      try {
        await this.ingest(symbols); // Fungsi asli kamu
        return;
      } catch (error) {
        this.logger.error('Ingestion loop error', error);
        await new Promise((resolve) => setTimeout(resolve, 5000));
        return;
      }
    }
  }

  async run(
    param: GetBacktestMethodDto,
    historicalData?: Historical[],
    historicalExecutionData?: Historical[],
    patternData?: Pattern[],
    instrumentData?: Instrument[],
  ) {
    try {
      if (
        !historicalData ||
        !historicalData.length ||
        !historicalExecutionData ||
        !historicalExecutionData.length ||
        !patternData ||
        !patternData.length ||
        !instrumentData ||
        !instrumentData.length
      )
        return;

      const key = `${param.symbol}-${param.interval}-${param.patternType}-${param.pattern}-${param.validityPeriod}-${param.lookbackPeriod}-${param.riskPercent}-${param.methodType}-${param.entryPercentByClose}-${param.rewardPercent}-${param.orderType}-${param.trend}`;
      const methodId = uuidv5(key, configurations('UUID_NAMESPACE'));
      param.methodId = methodId;

      const existingMethodStatus =
        await this.methodStatusService.getMethodStatusByMethodId(methodId);
      if (existingMethodStatus) return;

      const { result, performance } =
        await this.backtestMethodService.getBacktestMethodBoth(
          param,
          historicalData,
          historicalExecutionData,
          patternData,
          instrumentData,
        );
      if (isGoodMethod(result, performance)) {
        if (param.methodType === 'static' && param.enableOptimization) {
          const optimizedPlan: OptimizedStaticPlan =
            await this.optimizeStaticPlanService.optimize(
              param,
              historicalData,
              historicalExecutionData,
              patternData,
              instrumentData,
              undefined,
              result,
            );
          param = { ...param, ...optimizedPlan };
        }
        await this.methodService.insertMethodParam({
          ...param,
          enableOptimization: param.methodType === 'dynamic',
        });
        await this.methodService.insertMethodPerformance(performance);
        await this.methodService.insertMethodResult(result);
        await this.methodStatusService.insertMethodStatus(
          methodId,
          param.symbol,
          param.interval,
        );
      }

      return;
    } catch (error) {
      this.logger.error(
        'Ingestion process failed',
        logDetail({
          class: 'AppService',
          function: 'ingest',
          error,
        }),
      );
      return;
    }
  }

  async ingest(symbols: string[]) {
    try {
      const limit = configurations('HISTORICAL_EXECUTION_LIMIT');
      const sort = 'ASC';
      const methodTypes = shuffleArray(configurations('METHOD_TYPE'));
      const lookbackPeriods = shuffleArray(
        configurations('METHOD_SIGNAL_LOOKBACK_PERIOD'),
      );
      const intervals = configurations('INTERVALS');
      const validityPeriods = shuffleArray(
        configurations('METHOD_SIGNAL_VALIDITY_PERIOD'),
      );
      const riskPercentSamples = shuffleArray(
        configurations('METHOD_RISK_PERCENT_SAMPLES'),
      );
      const orderTypes = shuffleArray(configurations('ORDER_TYPES'));
      const patterns = shuffleArray(configurations('CANDLESTICK_PATTERNS'));
      const trends = shuffleArray(configurations('TRENDS'));
      const executionIntervals = configurations('EXECUTION_INTERVAL');
      const rewardPercentSamples = shuffleArray(
        configurations('METHOD_REWARD_PERCENT_SAMPLES'),
      );
      const entryPercentByCloses = shuffleArray(
        configurations('METHOD_ENTRY_PERCENT_BY_CLOSE_SAMPLES'),
      );
      const start = new Date(0);
      const end = new Date();

      for (const symbol of symbols) {
        const instrumentData = await this.instrumentService.getInstrument({
          symbol,
        });
        if (!instrumentData.length) continue;
        const historicalExecutionData =
          await this.historicalCacheService.getHistorical({
            symbol,
            start,
            interval: executionIntervals,
            end,
            limit,
            sort,
          });
        if (!historicalExecutionData.length) continue;
        for (const interval of intervals
          .filter((item: string) => item !== executionIntervals)
          .reverse()) {
          const historicalData =
            await this.historicalCacheService.getHistorical({
              symbol,
              start,
              interval,
              end,
              limit,
              sort,
            });
          if (!historicalData.length) continue;
          for (const trend of trends) {
            const patternType = 'candlestick';
            for (const pattern of patterns) {
              const patternData = await this.patternService.getPattern(
                {
                  symbol,
                  interval,
                  patternType,
                  pattern,
                  start,
                  end,
                  trend,
                  limit,
                  sort,
                },
                historicalData,
              );
              if (!patternData.length) continue;
              for (const validityPeriod of validityPeriods) {
                for (const entryPercentByClose of entryPercentByCloses) {
                  for (const orderType of orderTypes) {
                    for (const riskPercent of riskPercentSamples) {
                      for (const rewardPercent of rewardPercentSamples.filter(
                        (item: number) => item >= riskPercent,
                      )) {
                        for (const methodType of methodTypes) {
                          this.logger.info(
                            `Running ${symbol}-${interval}-${patternType}-${pattern}-${validityPeriod}-${entryPercentByClose}-${riskPercent}-${rewardPercent}-${orderType}-${trend}-${methodType}`,
                          );
                          if (methodType === 'dynamic') {
                            for (const lookbackPeriod of lookbackPeriods) {
                              await this.run(
                                {
                                  symbol,
                                  interval,
                                  patternType,
                                  pattern,
                                  validityPeriod,
                                  lookbackPeriod,
                                  riskPercent,
                                  methodType,
                                  entryPercentByClose,
                                  rewardPercent,
                                  start,
                                  end,
                                  orderType,
                                  trend,
                                  limit,
                                  sort,
                                  enableOptimization: true,
                                },
                                historicalData,
                                historicalExecutionData,
                                patternData,
                                instrumentData,
                              );
                            }
                          } else {
                            await this.run(
                              {
                                symbol,
                                interval,
                                patternType,
                                pattern,
                                validityPeriod,
                                lookbackPeriod: 0,
                                riskPercent,
                                methodType: 'static',
                                entryPercentByClose,
                                rewardPercent,
                                start,
                                end,
                                orderType,
                                trend,
                                limit,
                                sort,
                                enableOptimization: true,
                              },
                              historicalData,
                              historicalExecutionData,
                              patternData,
                              instrumentData,
                            );
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      return process.exit(0);
    } catch (error) {
      this.logger.error(
        'Ingestion process failed',
        logDetail({
          class: 'AppService',
          function: 'ingest',
          error,
        }),
      );
      return
    }
  }
}
