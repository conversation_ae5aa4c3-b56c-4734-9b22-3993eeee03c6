"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeResultEntity = void 0;
const typeorm_1 = require("typeorm");
let TradeResultEntity = class TradeResultEntity {
    id;
    category;
    symbol;
    isLeverage;
    side;
    orderType;
    qty;
    price;
    triggerDirection;
    triggerPrice;
    triggerBy;
    positionIdx;
    orderLinkId;
    orderId;
    takeProfit;
    stopLoss;
    tpTriggerBy;
    slTriggerBy;
    tpslMode;
    tpOrderType;
    slOrderType;
    methodId;
    date;
    interval;
    stopPercent;
    profitPercent;
    expiryDate;
    openDate;
    closedDate;
    status;
    createdAt;
    updatedAt;
};
exports.TradeResultEntity = TradeResultEntity;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "symbol", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 1 }),
    __metadata("design:type", Object)
], TradeResultEntity.prototype, "isLeverage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "side", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "orderType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "qty", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], TradeResultEntity.prototype, "triggerDirection", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "triggerPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'LastPrice' }),
    __metadata("design:type", Object)
], TradeResultEntity.prototype, "triggerBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], TradeResultEntity.prototype, "positionIdx", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', unique: true }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "orderLinkId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "orderId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "takeProfit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "stopLoss", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'LastPrice' }),
    __metadata("design:type", Object)
], TradeResultEntity.prototype, "tpTriggerBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'LastPrice' }),
    __metadata("design:type", Object)
], TradeResultEntity.prototype, "slTriggerBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'Partial' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "tpslMode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'Market' }),
    __metadata("design:type", Object)
], TradeResultEntity.prototype, "tpOrderType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: 'Market' }),
    __metadata("design:type", Object)
], TradeResultEntity.prototype, "slOrderType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "methodId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], TradeResultEntity.prototype, "date", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "interval", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], TradeResultEntity.prototype, "stopPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], TradeResultEntity.prototype, "profitPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], TradeResultEntity.prototype, "expiryDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], TradeResultEntity.prototype, "openDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], TradeResultEntity.prototype, "closedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        default: 'pending',
    }),
    __metadata("design:type", String)
], TradeResultEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], TradeResultEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], TradeResultEntity.prototype, "updatedAt", void 0);
exports.TradeResultEntity = TradeResultEntity = __decorate([
    (0, typeorm_1.Entity)('trade-result'),
    (0, typeorm_1.Index)(['methodId', 'date'])
], TradeResultEntity);
//# sourceMappingURL=trade-result.entity.js.map