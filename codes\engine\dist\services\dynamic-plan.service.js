"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DynamicPlanService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const static_plan_service_1 = require("./static-plan.service");
const optimize_static_plan_service_1 = require("./optimize-static-plan.service");
const instrument_service_1 = require("./instrument.service");
const to_milliseconds_util_1 = require("../util/to-milliseconds.util");
const get_expiry_date_util_1 = require("../util/get-expiry-date.util");
const get_entry_price_util_1 = require("../util/get-entry-price.util");
const get_target_price_util_1 = require("../util/get-target-price.util");
const log_detail_util_1 = require("../util/log-detail.util");
const pattern_service_1 = require("./pattern.service");
const configurations_1 = __importDefault(require("../configurations"));
const run_generate_result_util_1 = require("../util/run-generate-result.util");
const historical_cache_service_1 = require("./historical-cache.service");
let DynamicPlanService = class DynamicPlanService {
    logger;
    historicalCacheService;
    staticPlanService;
    optimizeStaticPlanService;
    instrumentService;
    patternService;
    constructor(logger, historicalCacheService, staticPlanService, optimizeStaticPlanService, instrumentService, patternService) {
        this.logger = logger;
        this.historicalCacheService = historicalCacheService;
        this.staticPlanService = staticPlanService;
        this.optimizeStaticPlanService = optimizeStaticPlanService;
        this.instrumentService = instrumentService;
        this.patternService = patternService;
    }
    async getPlan(param, historical, patterns, instrument, staticPlan) {
        try {
            const planData = [];
            const lookbackPeriod = param.lookbackPeriod ?? (0, configurations_1.default)('DEFAULT_LOOKBACK_PERIOD');
            const historicalData = historical ?? (await this.historicalCacheService.getHistorical(param));
            const patternData = patterns ??
                (await this.patternService.getPattern(param, historicalData));
            const instrumentData = instrument ??
                (await this.instrumentService.getInstrument({
                    symbol: param.symbol,
                }));
            const staticPlanData = staticPlan ??
                (await this.staticPlanService.getPlan(param, historicalData, patternData, instrumentData));
            const historicalDataExecution = await this.historicalCacheService.getHistorical({
                ...param,
                start: new Date(staticPlanData[0].date),
                interval: (0, configurations_1.default)('EXECUTION_INTERVAL'),
            });
            if (!staticPlanData.length)
                return [];
            if (!instrumentData.length)
                return [];
            const tickSize = instrumentData[0].priceFilter.tickSize;
            for (const item of staticPlanData) {
                const startDate = new Date(item.date.getTime() -
                    (0, to_milliseconds_util_1.toMiliseconds)(item.interval) * (lookbackPeriod + 1));
                const planSample = staticPlanData.filter((p) => p.date.getTime() >= startDate.getTime() &&
                    p.date.getTime() < item.date.getTime());
                if (!planSample.length)
                    continue;
                const backtestResult = (0, run_generate_result_util_1.runGenerateResult)(planSample, historicalDataExecution);
                const totalProfit = backtestResult.filter((item) => item.status === 'profit').length;
                const totalLoss = backtestResult.filter((item) => item.status === 'loss').length;
                const totalResults = totalProfit + totalLoss;
                if (!backtestResult.length ||
                    (totalProfit / totalResults) * 100 <
                        (0, configurations_1.default)('METHOD_MIN_PROBABILITY'))
                    continue;
                const optimizePlan = await this.optimizeStaticPlanService.optimize(param, historicalData, historicalDataExecution, patternData, instrumentData, planSample, backtestResult);
                const expiryDate = (0, get_expiry_date_util_1.getExpiryDate)(item);
                const entry = (0, get_entry_price_util_1.getEntry)({
                    orderType: item.orderType,
                    closePrice: item.entry * (1 / (1 + item.entryPercentByClose / 100)),
                    entryPercentByClose: optimizePlan.entryPercentByClose,
                    tickSize,
                });
                const stopLoss = (0, get_target_price_util_1.getTargetPrice)({
                    mathRound: item.orderType === 'long' ? 'floor' : 'ceil',
                    entry,
                    targetPercent: optimizePlan.riskPercent,
                    tickSize,
                });
                const takeProfit = (0, get_target_price_util_1.getTargetPrice)({
                    mathRound: item.orderType === 'long' ? 'floor' : 'ceil',
                    entry,
                    targetPercent: optimizePlan.rewardPercent,
                    tickSize,
                });
                const plan = {
                    ...item,
                    expiryDate,
                    entryPercentByClose: optimizePlan.entryPercentByClose,
                    entry,
                    riskPercent: optimizePlan.riskPercent,
                    stopLoss,
                    rewardPercent: optimizePlan.rewardPercent,
                    takeProfit,
                    stopPercent: ((stopLoss - entry) / entry) * 100,
                    profitPercent: ((takeProfit - entry) / entry) * 100,
                };
                planData.push(plan);
            }
            return planData;
        }
        catch (error) {
            this.logger.error('Failed to fetch pattern data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getPlan',
                param,
                error,
            }));
            return [];
        }
    }
};
exports.DynamicPlanService = DynamicPlanService;
exports.DynamicPlanService = DynamicPlanService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        historical_cache_service_1.HistoricalCacheService,
        static_plan_service_1.StaticPlanService,
        optimize_static_plan_service_1.OptimizeStaticPlanService,
        instrument_service_1.InstrumentService,
        pattern_service_1.PatternService])
], DynamicPlanService);
//# sourceMappingURL=dynamic-plan.service.js.map