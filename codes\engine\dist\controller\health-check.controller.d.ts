import { Logger } from 'winston';
import { HealthCheckService } from 'src/services/health-check.service';
export declare class HealthCheckController {
    private readonly healthCheckService;
    private readonly logger;
    constructor(healthCheckService: HealthCheckService, logger: Logger);
    liveness(): Promise<{
        status: string;
        timestamp: string;
    }>;
    readiness(): Promise<{
        status: string;
        timestamp: string;
        results: Record<string, 'reachable' | 'unreachable'>;
        message?: string;
    }>;
    startup(): Promise<{
        status: string;
        timestamp: string;
        results: Record<string, 'reachable' | 'unreachable'>;
    }>;
    check(): Promise<{
        status: string;
        timestamp: string;
        results: Record<string, 'reachable' | 'unreachable'>;
    }>;
}
