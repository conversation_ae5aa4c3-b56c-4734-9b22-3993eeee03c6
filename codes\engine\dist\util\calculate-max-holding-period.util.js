"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateMaxHoldingPeriod = calculateMaxHoldingPeriod;
function calculateMaxHoldingPeriod(param) {
    let maxHoldingDays = 0;
    for (const record of param) {
        const { openDate, closedDate, status } = record;
        if ((status === 'profit' || status === 'loss') && openDate && closedDate) {
            const open = new Date(openDate);
            const close = new Date(closedDate);
            const holdingDays = (close.getTime() - open.getTime()) / (1000 * 60 * 60 * 24);
            if (holdingDays > maxHoldingDays) {
                maxHoldingDays = holdingDays;
            }
        }
    }
    return Math.ceil(maxHoldingDays);
}
//# sourceMappingURL=calculate-max-holding-period.util.js.map