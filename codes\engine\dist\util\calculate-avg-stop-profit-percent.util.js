"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateAvgStopProfitPercent = calculateAvgStopProfitPercent;
function calculateAvgStopProfitPercent(param, target) {
    const total = param.reduce((acc, cur) => {
        if (target === 'stop' && cur.status === 'loss') {
            acc += Math.abs(cur.stopPercent);
        }
        if (target === 'profit' && cur.status === 'profit') {
            acc += Math.abs(cur.profitPercent);
        }
        return acc;
    }, 0);
    const avg = total / param.length;
    return Number(avg.toFixed(2));
}
//# sourceMappingURL=calculate-avg-stop-profit-percent.util.js.map