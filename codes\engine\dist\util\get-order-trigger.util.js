"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOrderTrigger = getOrderTrigger;
const round_to_tick_size_util_1 = require("./round-to-tick-size.util");
function getOrderTrigger(param) {
    const tick = param.tickSize;
    const entry = Number(param.entry);
    const last = Number(param.last);
    const isLastHigher = last > entry;
    const isLastLower = last < entry;
    const triggerDirection = isLastLower
        ? 1
        : isLastHigher
            ? 2
            : param.side === 'Buy'
                ? 1
                : 2;
    const triggerPrice = isLastHigher || isLastLower
        ? entry
        : param.side === 'Buy'
            ? entry + tick
            : entry - tick;
    const output = {
        triggerDirection,
        triggerPrice: (0, round_to_tick_size_util_1.roundToTickSize)({
            mathRound: 'floor',
            price: triggerPrice,
            tickSize: tick,
        }).toString(),
    };
    return output;
}
//# sourceMappingURL=get-order-trigger.util.js.map