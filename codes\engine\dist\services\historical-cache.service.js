"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoricalCacheService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
let HistoricalCacheService = class HistoricalCacheService {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    getYearMonth(date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        return `${year}_${month}`;
    }
    getMonthlyFilePaths(symbol, interval, start, end) {
        const basePath = path.join('data/historical', symbol, interval);
        if (!fs.existsSync(basePath)) {
            return [];
        }
        const files = fs
            .readdirSync(basePath)
            .filter((file) => file.endsWith('.csv') && file.match(/^\d{4}_\d{2}\.csv$/))
            .sort();
        if (!start && !end) {
            return files.map((file) => path.join(basePath, file));
        }
        const startYearMonth = start ? this.getYearMonth(start) : null;
        const endYearMonth = end ? this.getYearMonth(end) : null;
        return files
            .filter((file) => {
            const yearMonth = file.replace('.csv', '');
            if (startYearMonth && yearMonth < startYearMonth)
                return false;
            if (endYearMonth && yearMonth > endYearMonth)
                return false;
            return true;
        })
            .map((file) => path.join(basePath, file));
    }
    async writeWithRetry(filePath, data, retries = 3) {
        const tempFilePath = `${filePath}.tmp`;
        const startTime = Date.now();
        this.logger.debug(`Starting writeWithRetry for ${filePath}`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalCacheService',
            function: 'writeWithRetry',
            param: {
                filePath,
                tempFilePath,
                dataLength: data.length,
                retries,
                platform: process.platform,
            },
        }));
        for (let i = 0; i < retries; i++) {
            const attemptStartTime = Date.now();
            this.logger.debug(`Write attempt ${i + 1}/${retries} for ${filePath}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'writeWithRetry',
                param: { filePath, attempt: i + 1, retries },
            }));
            try {
                const csvContent = this.convertToCsv(data);
                this.logger.debug(`CSV content generated, size: ${csvContent.length} chars`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'writeWithRetry',
                    param: { filePath, csvSize: csvContent.length },
                }));
                fs.writeFileSync(tempFilePath, csvContent, 'utf-8');
                this.logger.debug(`Temp file written successfully: ${tempFilePath}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'writeWithRetry',
                    param: { tempFilePath },
                }));
                fs.renameSync(tempFilePath, filePath);
                this.logger.debug(`File renamed successfully from ${tempFilePath} to ${filePath}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'writeWithRetry',
                    param: { tempFilePath, filePath, duration: Date.now() - startTime },
                }));
                return;
            }
            catch (err) {
                const attemptDuration = Date.now() - attemptStartTime;
                this.logger.warn(`Write attempt ${i + 1}/${retries} failed for ${filePath}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'writeWithRetry',
                    error: err,
                    param: {
                        filePath,
                        attempt: i + 1,
                        retries,
                        attemptDuration,
                        willRetry: i < retries - 1,
                    },
                }));
                if (i === retries - 1)
                    throw err;
                const retryDelay = 100 * (i + 1);
                this.logger.debug(`Waiting ${retryDelay}ms before retry`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'writeWithRetry',
                    param: { filePath, retryDelay },
                }));
                await new Promise((resolve) => setTimeout(resolve, retryDelay));
            }
            finally {
                if (fs.existsSync(tempFilePath)) {
                    try {
                        fs.unlinkSync(tempFilePath);
                        this.logger.debug(`Temp file cleaned up: ${tempFilePath}`, (0, log_detail_util_1.logDetail)({
                            class: 'HistoricalCacheService',
                            function: 'writeWithRetry',
                            param: { tempFilePath },
                        }));
                    }
                    catch (cleanupErr) {
                        this.logger.warn('Failed to cleanup temp file', (0, log_detail_util_1.logDetail)({
                            class: 'HistoricalCacheService',
                            function: 'writeWithRetry',
                            error: cleanupErr,
                            param: tempFilePath,
                        }));
                    }
                }
            }
        }
    }
    convertToCsv(data) {
        if (!data.length)
            return 'date,symbol,interval,open,high,low,close,volume\n';
        const header = 'date,symbol,interval,open,high,low,close,volume\n';
        const rows = data
            .map((item) => `${new Date(item.date).toISOString()},${item.symbol},${item.interval},${item.open},${item.high},${item.low},${item.close},${item.volume}`)
            .join('\n');
        return header + rows;
    }
    parseCsvFile(filePath) {
        if (!fs.existsSync(filePath)) {
            this.logger.debug(`CSV file does not exist: ${filePath}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'parseCsvFile',
                param: { filePath },
            }));
            return [];
        }
        try {
            const content = fs.readFileSync(filePath, 'utf-8').trim();
            if (!content) {
                this.logger.debug(`CSV file is empty: ${filePath}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'parseCsvFile',
                    param: { filePath },
                }));
                return [];
            }
            const lines = content.split('\n');
            if (lines.length <= 1) {
                this.logger.debug(`CSV file has no data rows: ${filePath}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'parseCsvFile',
                    param: { filePath, lineCount: lines.length },
                }));
                return [];
            }
            const validRecords = [];
            const invalidRecords = [];
            lines.slice(1).forEach((line, index) => {
                try {
                    const [date, symbol, interval, open, high, low, close, volume] = line.split(',');
                    if (!date ||
                        !symbol ||
                        !interval ||
                        !open ||
                        !high ||
                        !low ||
                        !close ||
                        !volume) {
                        invalidRecords.push(`Line ${index + 2}: Missing fields`);
                        return;
                    }
                    const parsedDate = new Date(date);
                    if (isNaN(parsedDate.getTime())) {
                        invalidRecords.push(`Line ${index + 2}: Invalid date format`);
                        return;
                    }
                    const parsedOpen = parseFloat(open);
                    const parsedHigh = parseFloat(high);
                    const parsedLow = parseFloat(low);
                    const parsedClose = parseFloat(close);
                    const parsedVolume = parseFloat(volume);
                    if (isNaN(parsedOpen) ||
                        isNaN(parsedHigh) ||
                        isNaN(parsedLow) ||
                        isNaN(parsedClose) ||
                        isNaN(parsedVolume)) {
                        invalidRecords.push(`Line ${index + 2}: Invalid numeric values`);
                        return;
                    }
                    validRecords.push({
                        date: parsedDate,
                        symbol: symbol.trim(),
                        interval: interval.trim(),
                        open: parsedOpen,
                        high: parsedHigh,
                        low: parsedLow,
                        close: parsedClose,
                        volume: parsedVolume,
                    });
                }
                catch (lineErr) {
                    invalidRecords.push(`Line ${index + 2}: Parse error - ${lineErr.message}`);
                }
            });
            if (invalidRecords.length > 0) {
                this.logger.warn(`Found ${invalidRecords.length} invalid records in ${filePath}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'parseCsvFile',
                    param: {
                        filePath,
                        totalLines: lines.length - 1,
                        validRecords: validRecords.length,
                        invalidRecords: invalidRecords.length,
                        sampleErrors: invalidRecords.slice(0, 5),
                    },
                }));
            }
            this.logger.debug(`Parsed CSV file: ${validRecords.length} valid records from ${filePath}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'parseCsvFile',
                param: {
                    filePath,
                    validRecords: validRecords.length,
                    invalidRecords: invalidRecords.length,
                },
            }));
            return validRecords;
        }
        catch (err) {
            this.logger.error(`Failed to parse CSV file: ${filePath}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'parseCsvFile',
                error: err,
                param: { filePath },
            }));
            return [];
        }
    }
    async validateCache(symbol, interval, yearMonth) {
        if (yearMonth) {
            const filePath = path.join('data/historical', symbol, interval, `${yearMonth}.csv`);
            if (!fs.existsSync(filePath))
                return true;
            try {
                const data = this.parseCsvFile(filePath);
                if (!Array.isArray(data)) {
                    await this.writeWithRetry(filePath, []);
                    return false;
                }
                return true;
            }
            catch (err) {
                this.logger.warn(`Repairing corrupted cache file ${filePath}`);
                await this.writeWithRetry(filePath, []);
                return false;
            }
        }
        else {
            const filePaths = this.getMonthlyFilePaths(symbol, interval);
            let allValid = true;
            for (const filePath of filePaths) {
                try {
                    const data = this.parseCsvFile(filePath);
                    if (!Array.isArray(data)) {
                        await this.writeWithRetry(filePath, []);
                        allValid = false;
                    }
                }
                catch (err) {
                    this.logger.warn(`Repairing corrupted cache file ${filePath}`);
                    await this.writeWithRetry(filePath, []);
                    allValid = false;
                }
            }
            return allValid;
        }
    }
    async getHistorical(param) {
        const { symbol, interval, start, end, limit, sort } = param;
        if (!symbol || !interval) {
            this.logger.warn('Missing required parameters for getHistorical', (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'getHistorical',
                param: { symbol, interval },
            }));
            return [];
        }
        const filePaths = this.getMonthlyFilePaths(symbol, interval, start, end);
        if (filePaths.length === 0) {
            this.logger.debug(`No historical cache files found for ${symbol}-${interval}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'getHistorical',
                param: { symbol, interval, start, end },
            }));
            return [];
        }
        try {
            let allData = [];
            for (const filePath of filePaths) {
                if (fs.existsSync(filePath) && fs.statSync(filePath).size > 0) {
                    const fileData = this.parseCsvFile(filePath);
                    if (Array.isArray(fileData)) {
                        allData.push(...fileData);
                    }
                }
            }
            if (allData.length === 0) {
                this.logger.debug(`No data found in cache files for ${symbol}-${interval}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'getHistorical',
                    param: { symbol, interval, filesChecked: filePaths.length },
                }));
                return [];
            }
            if (start) {
                allData = allData.filter((d) => d.date >= start);
            }
            if (end) {
                allData = allData.filter((d) => d.date <= end);
            }
            allData.sort((a, b) => sort === 'DESC'
                ? b.date.getTime() - a.date.getTime()
                : a.date.getTime() - b.date.getTime());
            if (limit && limit > 0) {
                allData = allData.slice(0, limit);
            }
            this.logger.debug(`Returning ${allData.length} historical records for ${symbol}-${interval}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'getHistorical',
                param: {
                    symbol,
                    interval,
                    finalCount: allData.length,
                    filesUsed: filePaths.length,
                },
            }));
            return allData;
        }
        catch (err) {
            this.logger.error('Failed to read historical data', (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'getHistorical',
                error: err,
                param: { ...param, filePaths },
            }));
            return [];
        }
    }
    async insertHistorical(param) {
        const startTime = Date.now();
        this.logger.debug(`Starting insertHistorical`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalCacheService',
            function: 'insertHistorical',
            param: {
                recordCount: param.length,
                platform: process.platform,
                pid: process.pid,
            },
        }));
        if (!param.length) {
            this.logger.debug(`No data to insert, returning early`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'insertHistorical',
                param: { recordCount: 0 },
            }));
            return;
        }
        const symbol = param[0].symbol;
        const interval = param[0].interval;
        const dirPath = path.join('data/historical', symbol, interval);
        this.logger.debug(`Creating directory structure`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalCacheService',
            function: 'insertHistorical',
            param: {
                symbol,
                interval,
                dirPath,
                absolutePath: path.resolve(dirPath),
            },
        }));
        try {
            fs.mkdirSync(dirPath, { recursive: true });
            this.logger.debug(`Directory created successfully: ${dirPath}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'insertHistorical',
                param: { dirPath },
            }));
        }
        catch (dirErr) {
            this.logger.error(`Failed to create directory: ${dirPath}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'insertHistorical',
                error: dirErr,
                param: { dirPath },
            }));
            throw dirErr;
        }
        this.logger.debug(`Grouping data by year_month`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalCacheService',
            function: 'insertHistorical',
            param: { symbol, interval, totalRecords: param.length },
        }));
        const monthlyData = new Map();
        param.forEach((item) => {
            const yearMonth = this.getYearMonth(item.date);
            if (!monthlyData.has(yearMonth)) {
                monthlyData.set(yearMonth, []);
            }
            monthlyData.get(yearMonth).push(item);
        });
        this.logger.debug(`Data grouped into ${monthlyData.size} monthly files`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalCacheService',
            function: 'insertHistorical',
            param: {
                symbol,
                interval,
                monthlyFiles: Array.from(monthlyData.keys()),
                totalRecords: param.length,
            },
        }));
        let processedFiles = 0;
        for (const [yearMonth, data] of monthlyData) {
            const fileStartTime = Date.now();
            const filePath = path.join(dirPath, `${yearMonth}.csv`);
            this.logger.debug(`Processing monthly file ${++processedFiles}/${monthlyData.size}: ${yearMonth}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'insertHistorical',
                param: {
                    symbol,
                    interval,
                    yearMonth,
                    filePath,
                    newRecords: data.length,
                    fileIndex: processedFiles,
                    totalFiles: monthlyData.size,
                },
            }));
            let existingData = [];
            if (fs.existsSync(filePath)) {
                this.logger.debug(`File exists, parsing existing data: ${filePath}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'insertHistorical',
                    param: { filePath },
                }));
                existingData = this.parseCsvFile(filePath);
                this.logger.debug(`Parsed ${existingData.length} existing records from ${filePath}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'insertHistorical',
                    param: { filePath, existingRecords: existingData.length },
                }));
            }
            else {
                this.logger.debug(`File does not exist, creating new: ${filePath}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalCacheService',
                    function: 'insertHistorical',
                    param: { filePath },
                }));
            }
            this.logger.debug(`Merging and deduplicating data`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'insertHistorical',
                param: {
                    filePath,
                    existingRecords: existingData.length,
                    newRecords: data.length,
                },
            }));
            const map = new Map();
            [...existingData, ...data].forEach((item) => {
                map.set(new Date(item.date).toISOString(), item);
            });
            const merged = Array.from(map.values()).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
            this.logger.debug(`Merged data ready for write`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'insertHistorical',
                param: {
                    filePath,
                    finalRecords: merged.length,
                    duplicatesRemoved: existingData.length + data.length - merged.length,
                },
            }));
            await this.writeWithRetry(filePath, merged);
            const fileDuration = Date.now() - fileStartTime;
            this.logger.debug(`Completed processing file ${yearMonth} in ${fileDuration}ms`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'insertHistorical',
                param: {
                    symbol,
                    interval,
                    yearMonth,
                    filePath,
                    duration: fileDuration,
                    finalRecords: merged.length,
                },
            }));
        }
        const totalDuration = Date.now() - startTime;
        this.logger.info(`Completed insertHistorical for ${symbol}-${interval}`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalCacheService',
            function: 'insertHistorical',
            param: {
                symbol,
                interval,
                totalRecords: param.length,
                filesProcessed: monthlyData.size,
                totalDuration,
            },
        }));
    }
    async deleteHistorical(param) {
        if (param.yearMonth) {
            const filePath = path.join('data/historical', param.symbol, param.interval, `${param.yearMonth}.csv`);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }
        else {
            const dirPath = path.join('data/historical', param.symbol, param.interval);
            if (fs.existsSync(dirPath)) {
                fs.rmSync(dirPath, { recursive: true, force: true });
            }
        }
    }
    async countHistorical(symbol, interval, start, end) {
        if (!symbol || !interval) {
            this.logger.warn('Missing required parameters for countHistorical', (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'countHistorical',
                param: { symbol, interval },
            }));
            return 0;
        }
        const filePaths = this.getMonthlyFilePaths(symbol, interval, start, end);
        if (filePaths.length === 0) {
            return 0;
        }
        try {
            let totalCount = 0;
            for (const filePath of filePaths) {
                if (fs.existsSync(filePath) && fs.statSync(filePath).size > 0) {
                    let data = this.parseCsvFile(filePath);
                    if (Array.isArray(data)) {
                        if (start) {
                            data = data.filter((d) => d.date >= start);
                        }
                        if (end) {
                            data = data.filter((d) => d.date <= end);
                        }
                        totalCount += data.length;
                    }
                }
            }
            this.logger.debug(`Total count for ${symbol}-${interval}: ${totalCount}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'countHistorical',
                param: {
                    symbol,
                    interval,
                    totalCount,
                    filesChecked: filePaths.length,
                },
            }));
            return totalCount;
        }
        catch (err) {
            this.logger.error('Failed to count historical cache', (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'countHistorical',
                error: err,
                param: { symbol, interval, start, end, filePaths },
            }));
            return 0;
        }
    }
    async getAvailableMonths(symbol, interval) {
        const dirPath = path.join('data/historical', symbol, interval);
        if (!fs.existsSync(dirPath)) {
            return [];
        }
        return fs
            .readdirSync(dirPath)
            .filter((file) => file.endsWith('.csv') && file.match(/^\d{4}_\d{2}\.csv$/))
            .map((file) => file.replace('.csv', ''))
            .sort();
    }
    async getCacheInfo(symbol, interval) {
        const months = await this.getAvailableMonths(symbol, interval);
        if (months.length === 0) {
            return {
                totalMonths: 0,
                months: [],
                totalRecords: 0,
            };
        }
        let totalRecords = 0;
        let oldestRecord;
        let newestRecord;
        for (const month of months) {
            const filePath = path.join('data/historical', symbol, interval, `${month}.csv`);
            if (fs.existsSync(filePath)) {
                const data = this.parseCsvFile(filePath);
                if (Array.isArray(data) && data.length > 0) {
                    totalRecords += data.length;
                    const sortedData = data.sort((a, b) => a.date.getTime() - b.date.getTime());
                    const firstDate = sortedData[0].date;
                    const lastDate = sortedData[sortedData.length - 1].date;
                    if (!oldestRecord || firstDate < oldestRecord) {
                        oldestRecord = firstDate;
                    }
                    if (!newestRecord || lastDate > newestRecord) {
                        newestRecord = lastDate;
                    }
                }
            }
        }
        return {
            totalMonths: months.length,
            months,
            totalRecords,
            oldestRecord,
            newestRecord,
        };
    }
    async cleanupEmptyMonths(symbol, interval) {
        const months = await this.getAvailableMonths(symbol, interval);
        let cleanedCount = 0;
        for (const month of months) {
            const filePath = path.join('data/historical', symbol, interval, `${month}.csv`);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                if (stats.size === 0) {
                    fs.unlinkSync(filePath);
                    cleanedCount++;
                    this.logger.debug(`Cleaned up empty month file: ${filePath}`);
                }
                else {
                    const data = this.parseCsvFile(filePath);
                    if (!Array.isArray(data) || data.length === 0) {
                        fs.unlinkSync(filePath);
                        cleanedCount++;
                        this.logger.debug(`Cleaned up empty month file: ${filePath}`);
                    }
                }
            }
        }
        return cleanedCount;
    }
    async cleanupOldBackups(maxAgeDays = 7) {
        const now = Date.now();
        const maxAgeMs = maxAgeDays * 24 * 60 * 60 * 1000;
        if (!fs.existsSync('data/historical'))
            return;
        const symbolDirs = fs.readdirSync('data/historical');
        for (const symbolDir of symbolDirs) {
            const symbolPath = path.join('data/historical', symbolDir);
            if (!fs.statSync(symbolPath).isDirectory())
                continue;
            const intervalDirs = fs.readdirSync(symbolPath);
            for (const intervalDir of intervalDirs) {
                const intervalPath = path.join(symbolPath, intervalDir);
                if (!fs.statSync(intervalPath).isDirectory())
                    continue;
                const files = fs.readdirSync(intervalPath);
                for (const file of files) {
                    if (file.includes('.corrupted.') || file.includes('.backup.')) {
                        const filePath = path.join(intervalPath, file);
                        try {
                            const stats = fs.statSync(filePath);
                            if (now - stats.mtimeMs > maxAgeMs) {
                                fs.unlinkSync(filePath);
                                this.logger.debug(`Cleaned up old backup: ${filePath}`);
                            }
                        }
                        catch (err) {
                            this.logger.warn(`Failed to cleanup backup ${filePath}`, (0, log_detail_util_1.logDetail)({ error: err }));
                        }
                    }
                }
            }
        }
    }
};
exports.HistoricalCacheService = HistoricalCacheService;
exports.HistoricalCacheService = HistoricalCacheService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger])
], HistoricalCacheService);
//# sourceMappingURL=historical-cache.service.js.map