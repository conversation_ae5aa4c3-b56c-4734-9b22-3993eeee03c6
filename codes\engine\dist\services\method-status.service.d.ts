import { MethodStatusEntity } from 'src/entity/method-status.entity';
import { MethodStatus } from 'src/interface/method-status.interface';
import { Repository, UpdateResult } from 'typeorm';
import { Logger } from 'winston';
import { MethodService } from './method.service';
export declare class MethodStatusService {
    private readonly logger;
    private readonly MethodsStatusRepository;
    private readonly methodService;
    constructor(logger: Logger, MethodsStatusRepository: Repository<MethodStatusEntity>, methodService: MethodService);
    getMethodStatusByMethodId(methodId: string): Promise<MethodStatusEntity | null>;
    getMethodStatus(): Promise<MethodStatusEntity[] | null>;
    getAllMethodStatus(): Promise<MethodStatusEntity[]>;
    insertMethodStatus(methodId: string, symbol: string, interval: string): Promise<void>;
    updateMethodStatus(param: MethodStatus): Promise<UpdateResult>;
    resetMethodStatus(interval: string): Promise<UpdateResult>;
    deleteMethodStatus(methodId: string): Promise<void>;
    resetMethodStatusByRunningResult(): Promise<UpdateResult>;
}
