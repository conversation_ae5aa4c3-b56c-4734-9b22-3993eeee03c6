"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateEquityRatio = calculateEquityRatio;
const configurations_1 = __importDefault(require("../configurations"));
function calculateEquityRatio(param) {
    const estAvgDrawdown = (0, configurations_1.default)('ESTIMATED_MAX_DRAWDOWN');
    const optimumEquityAllocation = (0, configurations_1.default)('OPTIMUM_EQUITY_ALLOCATION');
    const ratioBasedOnMaxLeverage = param.minOfMaxLeverage / param.maxOpenPosition;
    const ratioBasedOnEstDrawdown = estAvgDrawdown / param.maxConsecutiveLoss / param.maxStopPercent;
    const ratioBasedOnMaxFundingRate = 1 -
        (param.maxHoldingPeriod *
            (1440 / param.minFundingInterval) *
            param.maxFundingRate *
            param.maxOpenPosition) /
            100;
    return Math.min(Math.abs(ratioBasedOnMaxLeverage), Math.abs(ratioBasedOnEstDrawdown), Math.abs(ratioBasedOnMaxFundingRate), Math.abs(optimumEquityAllocation));
}
//# sourceMappingURL=calculate-equity-ratio.util.js.map