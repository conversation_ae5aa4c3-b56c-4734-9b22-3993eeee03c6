{"version": 3, "file": "optimize-static-plan.service.js", "sourceRoot": "", "sources": ["../../src/services/optimize-static-plan.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AACvD,qCAAiC;AACjC,yDAAqD;AAErD,uEAAgD;AAMhD,uDAAmD;AACnD,+DAA0D;AAI1D,6DAAyD;AACzD,6DAAqD;AACrD,yEAAoE;AAG7D,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IAEgB;IACjC;IACA;IACA;IACA;IACA;IANnB,YACoD,MAAc,EAC/C,eAAgC,EAChC,sBAA8C,EAC9C,cAA8B,EAC9B,iBAAoC,EACpC,iBAAoC;QALH,WAAM,GAAN,MAAM,CAAQ;QAC/C,oBAAe,GAAf,eAAe,CAAiB;QAChC,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,mBAAc,GAAd,cAAc,CAAgB;QAC9B,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,sBAAiB,GAAjB,iBAAiB,CAAmB;IACpD,CAAC;IAEJ,KAAK,CAAC,QAAQ,CACZ,KAAuB,EACvB,UAAyB,EACzB,mBAAkC,EAClC,QAAoB,EACpB,UAAyB,EACzB,IAAa,EACb,OAAwB;QAExB,IAAI,CAAC;YACH,MAAM,cAAc,GAClB,UAAU,IAAI,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;YAEzE,MAAM,uBAAuB,GAC3B,mBAAmB;gBACnB,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;oBAC/C,GAAG,KAAK;oBACR,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,QAAQ,EAAE,IAAA,wBAAc,EAAC,oBAAoB,CAAC;iBAC/C,CAAC,CAAC,CAAC;YAEN,MAAM,WAAW,GACf,QAAQ;gBACR,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;YAEhE,MAAM,cAAc,GAClB,UAAU;gBACV,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;oBAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;iBACrB,CAAC,CAAC,CAAC;YAEN,MAAM,QAAQ,GACZ,IAAI;gBACJ,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CACnC,KAAK,EACL,cAAc,EACd,WAAW,EACX,cAAc,CACf,CAAC,CAAC;YAEL,MAAM,WAAW,GACf,OAAO;gBACP,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAC/C,QAAQ,EACR,uBAAuB,CACxB,CAAC,CAAC;YAEL,MAAM,aAAa,GAAwB;gBACzC,mBAAmB,EAAE,CAAC;gBACtB,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,CAAC;aACf,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,MAAM;gBAAE,OAAO,aAAa,CAAC;YAE9C,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CACtC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,CACnC,CAAC;YAEF,IAAI,CAAC,aAAa,CAAC,MAAM;gBAAE,OAAO,aAAa,CAAC;YAEhD,MAAM,eAAe,GAAa,EAAE,CAAC;YACrC,MAAM,gBAAgB,GAAa,EAAE,CAAC;YACtC,MAAM,cAAc,GAAa,EAAE,CAAC;YAEpC,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;gBACjC,MAAM,OAAO,GAAG,cAAc,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAChD,CAAC;gBACF,IAAI,CAAC,OAAO;oBAAE,SAAS;gBACvB,MAAM,0BAA0B,GAAG,cAAc,CAAC,MAAM,CACtD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAC9C,CAAC;gBACF,IAAI,CAAC,0BAA0B,CAAC,MAAM;oBAAE,SAAS;gBAEjD,MAAM,cAAc,GAAG,0BAA0B,CAAC,CAAC,CAAC,CAAC;gBAErD,MAAM,yBAAyB,GAAG,uBAAuB,CAAC,MAAM,CAC9D,CAAC,cAAc,EAAE,EAAE,CACjB,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,CACjE,CAAC;gBAEF,IAAI,CAAC,yBAAyB,CAAC,MAAM;oBAAE,SAAS;gBAEhD,IAAI,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;gBAE1B,MAAM,QAAQ,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CACtD,KAAK,CAAC,SAAS,KAAK,MAAM;oBACxB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ;oBACxB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAC5B,CAAC;gBAEF,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACpB,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC9B,CAAC;gBAED,MAAM,kCAAkC,GACtC,yBAAyB,CAAC,MAAM,CAC9B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,CAC7C,CAAC;gBACJ,IAAI,kCAAkC,CAAC,MAAM,KAAK,CAAC;oBAAE,SAAS;gBAE9D,MAAM,UAAU,GACd,KAAK,CAAC,SAAS,KAAK,MAAM;oBACxB,CAAC,CAAC,kCAAkC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACvD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CACpC;oBACH,CAAC,CAAC,kCAAkC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACvD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAClC,CAAC;gBAER,MAAM,WAAW,GACf,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC;oBAC/D,OAAO,CAAC,KAAK,CAAC;gBAChB,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAEnC,MAAM,gCAAgC,GACpC,kCAAkC,CAAC,MAAM,CACvC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CACpD,CAAC;gBACJ,IAAI,gCAAgC,CAAC,MAAM,KAAK,CAAC;oBAAE,SAAS;gBAE5D,MAAM,QAAQ,GACZ,KAAK,CAAC,SAAS,KAAK,MAAM;oBACxB,CAAC,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACrD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAClC;oBACH,CAAC,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACrD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CACpC,CAAC;gBAER,MAAM,SAAS,GACb,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAC3D,OAAO,CAAC,KAAK,CAAC;gBAEhB,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAE/B,MAAM,iCAAiC,GACrC,kCAAkC,CAAC,MAAM,CACvC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CACpD,CAAC;gBAEJ,IAAI,CAAC,iCAAiC,CAAC,MAAM;oBAAE,SAAS;gBAExD,MAAM,SAAS,GACb,IAAI,CAAC,SAAS,KAAK,MAAM;oBACvB,CAAC,CAAC,iCAAiC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACtD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAClC;oBACH,CAAC,CAAC,iCAAiC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACtD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CACpC,CAAC;gBAER,MAAM,UAAU,GACd,CAAC,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC;oBAC5D,OAAO,CAAC,KAAK,CAAC;gBAChB,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACnC,CAAC;YAED,IACE,CAAC,gBAAgB,CAAC,MAAM;gBACxB,CAAC,cAAc,CAAC,MAAM;gBACtB,CAAC,eAAe,CAAC,MAAM,EACvB,CAAC;gBACD,OAAO,aAAa,CAAC;YACvB,CAAC;YAED,MAAM,mBAAmB,GACvB,GAAG;gBACH,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM;oBACzB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC;oBAClC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;YAExC,MAAM,aAAa,GACjB,GAAG;gBACH,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM;oBACzB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC;oBAClE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;YAExE,MAAM,kBAAkB,GACtB,GAAG;gBACH,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM;oBACzB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC;oBAChE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;YAEtE,MAAM,WAAW,GACf,KAAK,CAAC,SAAS,KAAK,MAAM,IAAI,kBAAkB,GAAG,CAAC,IAAI;gBACtD,CAAC,CAAC,kBAAkB,GAAG,IAAI;gBAC3B,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,OAAO,IAAI,kBAAkB,GAAG,IAAI;oBACxD,CAAC,CAAC,kBAAkB,GAAG,IAAI;oBAC3B,CAAC,CAAC,kBAAkB,CAAC;YAC3B,OAAO;gBACL,mBAAmB;gBACnB,aAAa;gBACb,WAAW;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,EAC9B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,SAAS;gBACnB,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO;gBACL,mBAAmB,EAAE,CAAC;gBACtB,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,CAAC;aACf,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,KAAuB,EACvB,UAAyB,EACzB,mBAAkC,EAClC,QAAoB,EACpB,UAAyB,EACzB,IAAa,EACb,OAAwB;QAExB,IAAI,CAAC;YACH,MAAM,cAAc,GAClB,UAAU,IAAI,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;YAEzE,MAAM,uBAAuB,GAC3B,mBAAmB;gBACnB,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;oBAC/C,GAAG,KAAK;oBACR,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,QAAQ,EAAE,IAAA,wBAAc,EAAC,oBAAoB,CAAC;iBAC/C,CAAC,CAAC,CAAC;YAEN,MAAM,WAAW,GACf,QAAQ;gBACR,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;YAEhE,MAAM,cAAc,GAClB,UAAU;gBACV,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;oBAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;iBACrB,CAAC,CAAC,CAAC;YAEN,MAAM,QAAQ,GACZ,IAAI;gBACJ,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CACnC,KAAK,EACL,cAAc,EACd,WAAW,EACX,cAAc,CACf,CAAC,CAAC;YAEL,MAAM,WAAW,GACf,OAAO;gBACP,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAC/C,QAAQ,EACR,uBAAuB,CACxB,CAAC,CAAC;YAEL,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,KAAK,EACL,cAAc,EACd,uBAAuB,EACvB,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,CACZ,CAAC;YACF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CACxD,EAAE,GAAG,KAAK,EAAE,GAAG,cAAc,EAAE,EAC/B,cAAc,EACd,WAAW,EACX,cAAc,CACf,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,EAC9B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,SAAS;gBACnB,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF,CAAA;AA5SY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;QAC9B,kCAAe;QACR,iDAAsB;QAC9B,gCAAc;QACX,uCAAiB;QACjB,sCAAiB;GAP5C,yBAAyB,CA4SrC"}