"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExternalAdapterService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const axios_1 = __importDefault(require("axios"));
const configurations_1 = __importDefault(require("../configurations"));
const log_detail_util_1 = require("../util/log-detail.util");
let ExternalAdapterService = class ExternalAdapterService {
    logger;
    httpClient;
    baseUrl;
    apiKey;
    constructor(logger) {
        this.logger = logger;
        this.baseUrl = (0, configurations_1.default)('ADAPTER_EXTERNAL_URL');
        this.apiKey = (0, configurations_1.default)('ADAPTER_EXTERNAL_API_KEY');
        if (!this.baseUrl) {
            this.logger.error('ADAPTER_EXTERNAL_URL is required when ADAPTER_ENABLED is true', (0, log_detail_util_1.logDetail)({
                class: 'ExternalAdapterService',
                function: 'constructor',
            }));
            return;
        }
        if (!this.apiKey) {
            this.logger.error('ADAPTER_EXTERNAL_API_KEY is required when ADAPTER_ENABLED is true', (0, log_detail_util_1.logDetail)({
                class: 'ExternalAdapterService',
                function: 'constructor',
            }));
            return;
        }
        this.httpClient = axios_1.default.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${this.apiKey}`,
            },
        });
        this.httpClient.interceptors.request.use((config) => {
            this.logger.debug('External adapter request', (0, log_detail_util_1.logDetail)({
                class: 'ExternalAdapterService',
                function: 'request',
                url: config.url,
                param: { method: config.method?.toUpperCase(), data: config.data },
            }));
            return config;
        }, (error) => {
            this.logger.error('External adapter request error', (0, log_detail_util_1.logDetail)({
                class: 'ExternalAdapterService',
                function: 'request',
                error: error.message,
            }));
            return Promise.reject(error);
        });
        this.httpClient.interceptors.response.use((response) => {
            this.logger.debug('External adapter response', (0, log_detail_util_1.logDetail)({
                class: 'ExternalAdapterService',
                function: 'response',
                status: response.status,
                param: { url: response.config.url, data: response.data },
            }));
            return response;
        }, (error) => {
            this.logger.error('External adapter response error', (0, log_detail_util_1.logDetail)({
                class: 'ExternalAdapterService',
                function: 'response',
                status: error.response?.status,
                url: error.config?.url,
                error: error.response?.data || error.message,
            }));
            return Promise.reject(error);
        });
    }
    async switchToHedgeMode(param) {
        const response = await this.httpClient.post('/adapter/switch-to-hedge-mode', param);
        return response.data;
    }
    async setLeverage(param) {
        const response = await this.httpClient.post('/adapter/set-leverage', param);
        return response.data;
    }
    async getWalletBalance() {
        const response = await this.httpClient.post('/adapter/get-wallet-balance');
        return response.data;
    }
    async placeOrder(param) {
        const response = await this.httpClient.post('/adapter/place-order', param);
        return response.data;
    }
    async amendOrder(param) {
        const response = await this.httpClient.post('/adapter/amend-order', param);
        return response.data;
    }
    async cancelOrder(param) {
        const response = await this.httpClient.post('/adapter/cancel-order', param);
        return response.data;
    }
    async getActiveOrders(param) {
        const response = await this.httpClient.post('/adapter/active-orders', param);
        return response.data;
    }
    async getOrderHistory(param) {
        const response = await this.httpClient.post('/adapter/order-history', param);
        return response.data;
    }
    async getHistorical(param) {
        const response = await this.httpClient.post('/adapter/historical', param);
        return response.data?.map((item) => ({
            ...item,
            date: new Date(item.date),
        }));
    }
    async getInstrument(param) {
        const response = await this.httpClient.post('/adapter/instrument', param);
        return response.data;
    }
};
exports.ExternalAdapterService = ExternalAdapterService;
exports.ExternalAdapterService = ExternalAdapterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger])
], ExternalAdapterService);
//# sourceMappingURL=external-adapter.service.js.map