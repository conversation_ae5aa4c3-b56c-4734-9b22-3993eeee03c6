{"version": 3, "file": "api-key.guard.js", "sourceRoot": "", "sources": ["../../src/guards/api-key.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAMwB;AAExB,+CAAuD;AACvD,qCAAiC;AACjC,uEAAgD;AAChD,6DAAqD;AAG9C,IAAM,WAAW,GAAjB,MAAM,WAAW;IAE8B;IADpD,YACoD,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAC/D,CAAC;IAEJ,WAAW,CAAC,OAAyB;QACnC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,8BAA8B,EAC9B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,aAAa;gBACpB,QAAQ,EAAE,aAAa;gBACvB,KAAK,EAAE;oBACL,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;oBACpC,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,EAAE,EAAE,OAAO,CAAC,EAAE;iBACf;aACF,CAAC,CACH,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,WAAW,GAAG,IAAA,wBAAc,EAAC,0BAA0B,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,wDAAwD,EACxD,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,aAAa;gBACpB,QAAQ,EAAE,aAAa;aACxB,CAAC,CACH,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,0BAA0B,EAC1B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,aAAa;gBACpB,QAAQ,EAAE,aAAa;gBACvB,KAAK,EAAE;oBACL,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;oBACpC,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,cAAc,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK;iBAC/C;aACF,CAAC,CACH,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,+BAA+B,EAC/B,IAAA,2BAAS,EAAC;YACR,KAAK,EAAE,aAAa;YACpB,QAAQ,EAAE,aAAa;YACvB,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE;SAC9C,CAAC,CACH,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,wBAAwB,CAAC,OAAgB;QAE/C,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAChD,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AA3EY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;GAFvD,WAAW,CA2EvB"}