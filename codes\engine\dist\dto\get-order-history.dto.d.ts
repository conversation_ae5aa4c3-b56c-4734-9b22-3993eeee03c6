export declare class GetOrderHistoryDto {
    category: 'spot' | 'linear' | 'inverse' | 'option';
    symbol?: string;
    baseCoin?: string;
    settleCoin?: string;
    orderId?: string;
    orderLinkId?: string;
    orderStatus?: 'Created' | 'New' | 'Rejected' | 'PartiallyFilled' | 'PartiallyFilledCanceled' | 'Filled' | 'Cancelled' | 'Untriggered' | 'Triggered' | 'Deactivated' | 'Active';
    orderFilter?: 'Order' | 'tpslOrder' | 'StopOrder';
    limit?: number;
    cursor?: string;
}
