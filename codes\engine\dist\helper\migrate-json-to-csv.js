"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
function convertToCsv(data) {
    if (!data.length)
        return 'date,symbol,interval,open,high,low,close,volume\n';
    const header = 'date,symbol,interval,open,high,low,close,volume\n';
    const rows = data.map(item => `${new Date(item.date).toISOString()},${item.symbol},${item.interval},${item.open},${item.high},${item.low},${item.close},${item.volume}`).join('\n');
    return header + rows;
}
function migrateJsonToCsv() {
    const dataDir = 'data/historical';
    if (!fs.existsSync(dataDir)) {
        console.log('No historical data directory found');
        return;
    }
    const symbolDirs = fs.readdirSync(dataDir);
    let migratedCount = 0;
    for (const symbolDir of symbolDirs) {
        const symbolPath = path.join(dataDir, symbolDir);
        if (!fs.statSync(symbolPath).isDirectory())
            continue;
        const files = fs.readdirSync(symbolPath);
        for (const file of files) {
            if (file.endsWith('.json')) {
                const jsonPath = path.join(symbolPath, file);
                const csvPath = path.join(symbolPath, file.replace('.json', '.csv'));
                try {
                    const jsonContent = fs.readFileSync(jsonPath, 'utf-8');
                    const data = JSON.parse(jsonContent);
                    if (Array.isArray(data)) {
                        const csvContent = convertToCsv(data);
                        fs.writeFileSync(csvPath, csvContent, 'utf-8');
                        const backupPath = `${jsonPath}.backup`;
                        fs.renameSync(jsonPath, backupPath);
                        console.log(`Migrated: ${jsonPath} -> ${csvPath}`);
                        migratedCount++;
                    }
                }
                catch (err) {
                    console.error(`Failed to migrate ${jsonPath}:`, err);
                }
            }
        }
    }
    console.log(`Migration completed. ${migratedCount} files migrated.`);
}
migrateJsonToCsv();
//# sourceMappingURL=migrate-json-to-csv.js.map