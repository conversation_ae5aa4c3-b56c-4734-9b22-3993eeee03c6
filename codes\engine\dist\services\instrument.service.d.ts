import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { InstrumentEntity } from 'src/entity/instrument.entity';
import { Instrument } from 'src/interface/instrument.interface';
import { Repository } from 'typeorm';
import { Logger } from 'winston';
export declare class InstrumentService {
    private readonly InstrumentsRepository;
    private readonly logger;
    constructor(InstrumentsRepository: Repository<InstrumentEntity>, logger: Logger);
    getMinOfMaxLeverage(): Promise<number>;
    getAvgOfMaxLeverage(): Promise<number>;
    getMaxLeverage(symbol: string): Promise<number>;
    getMaxFundingRate(): Promise<number>;
    getAvgFundingRate(): Promise<number>;
    getMinFundingInterval(): Promise<number>;
    getAvgFundingInterval(): Promise<number>;
    getMinOrderQty(symbol: string): Promise<number>;
    getMaxOrderQty(symbol: string): Promise<number>;
    getQtyStep(symbol: string): Promise<number>;
    getInstrument(param: GetInstrumentDto): Promise<InstrumentEntity[]>;
    insert(param: Instrument[], enableListedTime?: boolean): Promise<InstrumentEntity[]>;
    getTickSize(symbol: string): Promise<number>;
    getSymbols(): Promise<string[]>;
}
