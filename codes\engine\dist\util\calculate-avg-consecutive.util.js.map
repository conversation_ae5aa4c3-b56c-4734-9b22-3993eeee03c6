{"version": 3, "file": "calculate-avg-consecutive.util.js", "sourceRoot": "", "sources": ["../../src/util/calculate-avg-consecutive.util.ts"], "names": [], "mappings": ";;AAEA,0DA6BC;AA7BD,SAAgB,uBAAuB,CACrC,KAAqB,EACrB,MAAyB;IAEzB,MAAM,gBAAgB,GAAa,EAAE,CAAC;IACtC,IAAI,WAAW,GAAG,CAAC,CAAC;IAEpB,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACvC,MAAM,WAAW,GAAG,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC;QAC3C,MAAM,WAAW,GAAG,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC;QAC3C,OAAO,WAAW,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;IACvD,CAAC,CAAC,EAAE,CAAC;QACH,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC7B,WAAW,EAAE,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;gBACpB,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACrC,CAAC;YACD,WAAW,GAAG,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;QACpB,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;IAED,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;IAClE,MAAM,GAAG,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC"}