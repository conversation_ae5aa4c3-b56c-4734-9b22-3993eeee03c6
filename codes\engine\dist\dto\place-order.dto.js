"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlaceOrderDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class PlaceOrderDto {
    category;
    symbol;
    isLeverage;
    side;
    orderType;
    qty;
    marketUnit;
    slippageToleranceType;
    slippageTolerance;
    price;
    triggerDirection;
    orderFilter;
    triggerPrice;
    triggerBy;
    orderIv;
    timeInForce;
    positionIdx;
    orderLinkId;
    takeProfit;
    stopLoss;
    tpTriggerBy;
    slTriggerBy;
    reduceOnly;
    closeOnTrigger;
    smpType;
    mmp;
    tpslMode;
    tpLimitPrice;
    slLimitPrice;
    tpOrderType;
    slOrderType;
}
exports.PlaceOrderDto = PlaceOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Product category',
        example: 'linear',
        enum: ['spot', 'linear', 'inverse', 'option'],
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['spot', 'linear', 'inverse', 'option']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Symbol name',
        example: 'BTCUSDT',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "symbol", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether to borrow. Unified spot only. 0(default): false, 1: true',
        example: 0,
        enum: [0, 1],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([0, 1]),
    (0, class_transformer_1.Transform)(({ value }) => Number(value)),
    __metadata("design:type", Number)
], PlaceOrderDto.prototype, "isLeverage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Buy or Sell',
        example: 'Buy',
        enum: ['Buy', 'Sell'],
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['Buy', 'Sell']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "side", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Order type',
        example: 'Market',
        enum: ['Market', 'Limit'],
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['Market', 'Limit']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "orderType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Order quantity',
        example: '0.01',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "qty", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The unit for qty when creating Spot market orders for UTA account',
        example: 'baseCoin',
        enum: ['baseCoin', 'quoteCoin'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['baseCoin', 'quoteCoin']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "marketUnit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Slippage tolerance type',
        example: 'percentage',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "slippageToleranceType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Slippage tolerance',
        example: '0.1',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "slippageTolerance", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Order price. Required for Limit orders',
        example: '50000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trigger direction. 1: rise, 2: fall',
        example: 1,
        enum: [1, 2],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([1, 2]),
    (0, class_transformer_1.Transform)(({ value }) => Number(value)),
    __metadata("design:type", Number)
], PlaceOrderDto.prototype, "triggerDirection", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Order filter',
        example: 'Order',
        enum: ['Order', 'tpslOrder', 'StopOrder'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['Order', 'tpslOrder', 'StopOrder']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "orderFilter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trigger price',
        example: '51000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "triggerPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trigger price type',
        example: 'LastPrice',
        enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['LastPrice', 'IndexPrice', 'MarkPrice']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "triggerBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Implied volatility',
        example: '0.1',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "orderIv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Time in force',
        example: 'GTC',
        enum: ['GTC', 'IOC', 'FOK', 'PostOnly', 'RPI'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['GTC', 'IOC', 'FOK', 'PostOnly', 'RPI']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "timeInForce", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Position index. 0: one-way mode, 1: hedge-mode Buy side, 2: hedge-mode Sell side',
        example: 0,
        enum: [0, 1, 2],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([0, 1, 2]),
    (0, class_transformer_1.Transform)(({ value }) => Number(value)),
    __metadata("design:type", Number)
], PlaceOrderDto.prototype, "positionIdx", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User customised order ID',
        example: 'my-order-001',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "orderLinkId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Take profit price',
        example: '55000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "takeProfit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Stop loss price',
        example: '45000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "stopLoss", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Take profit trigger price type',
        example: 'LastPrice',
        enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['LastPrice', 'IndexPrice', 'MarkPrice']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "tpTriggerBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Stop loss trigger price type',
        example: 'LastPrice',
        enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['LastPrice', 'IndexPrice', 'MarkPrice']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "slTriggerBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Reduce only. true means your position can only reduce in size if this order is triggered',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PlaceOrderDto.prototype, "reduceOnly", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Close on trigger',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PlaceOrderDto.prototype, "closeOnTrigger", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'SMP execution type',
        example: 'None',
        enum: ['None', 'CancelMaker', 'CancelTaker', 'CancelBoth'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['None', 'CancelMaker', 'CancelTaker', 'CancelBoth']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "smpType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Market maker protection',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PlaceOrderDto.prototype, "mmp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'TP/SL mode',
        example: 'Full',
        enum: ['Full', 'Partial'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['Full', 'Partial']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "tpslMode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Take profit limit price',
        example: '54000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "tpLimitPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Stop loss limit price',
        example: '46000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "slLimitPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Take profit order type',
        example: 'Market',
        enum: ['Market', 'Limit'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['Market', 'Limit']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "tpOrderType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Stop loss order type',
        example: 'Market',
        enum: ['Market', 'Limit'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['Market', 'Limit']),
    __metadata("design:type", String)
], PlaceOrderDto.prototype, "slOrderType", void 0);
//# sourceMappingURL=place-order.dto.js.map