import { OrderTriggerByV5, OrderTypeV5 } from 'bybit-api';
export declare class TradeResultEntity {
    id: string;
    category: 'linear' | 'inverse' | 'spot' | 'option';
    symbol: string;
    isLeverage: 0 | 1 | undefined;
    side: 'Buy' | 'Sell';
    orderType: 'Limit' | 'Market';
    qty: string;
    price: string;
    triggerDirection: 1 | 2;
    triggerPrice: string;
    triggerBy: OrderTriggerByV5 | undefined;
    positionIdx: 0 | 1 | 2;
    orderLinkId: string;
    orderId?: string;
    takeProfit: string;
    stopLoss: string;
    tpTriggerBy: OrderTriggerByV5 | undefined;
    slTriggerBy: OrderTriggerByV5 | undefined;
    tpslMode: 'Full' | 'Partial';
    tpOrderType: OrderTypeV5 | undefined;
    slOrderType: OrderTypeV5 | undefined;
    methodId: string;
    date: Date;
    interval: string;
    stopPercent: number;
    profitPercent: number;
    expiryDate: Date;
    openDate?: Date;
    closedDate?: Date;
    status: 'pending' | 'expired' | 'open' | 'profit' | 'loss' | 'invalid' | 'canceled';
    createdAt: Date;
    updatedAt: Date;
}
