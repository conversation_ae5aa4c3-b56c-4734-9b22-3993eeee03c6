{"version": 3, "file": "historical.service.js", "sourceRoot": "", "sources": ["../../src/services/historical.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,+CAAuD;AAEvD,mEAAgE;AAEhE,6DAAqD;AACrD,qCAAqC;AACrC,qCAAiC;AAG1B,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAEwB;IAEjC;IAHnB,YACoD,MAAc,EAE/C,qBAAmD;QAFlB,WAAM,GAAN,MAAM,CAAQ;QAE/C,0BAAqB,GAArB,qBAAqB,CAA8B;IACnE,CAAC;IAEJ,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,QAAgB,EAChB,KAAY,EACZ,GAAU;QAEV,IAAI,CAAC;YACH,MAAM,YAAY,GAChB,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAG9D,IAAI,MAAM,EAAE,CAAC;gBACX,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YACnE,CAAC;YAED,IAAI,QAAQ,EAAE,CAAC;gBACb,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE;oBACvD,QAAQ;iBACT,CAAC,CAAC;YACL,CAAC;YAED,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,CAAC,QAAQ,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,GAAG,EAAE,CAAC;gBACR,YAAY,CAAC,QAAQ,CAAC,yBAAyB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO,MAAM,YAAY,CAAC,QAAQ,EAAE,CAAC;QACvC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,EACjC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,OAAO;gBACjB,KAAK,EAAE,GAAG;gBACV,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE;aACxC,CAAC,CACH,CAAC;YACF,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,KAAuB;QACzC,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;YAE5D,MAAM,YAAY,GAChB,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAG9D,IAAI,MAAM,EAAE,CAAC;gBACX,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YACnE,CAAC;YAED,IAAI,QAAQ,EAAE,CAAC;gBACb,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE;oBACvD,QAAQ;iBACT,CAAC,CAAC;YACL,CAAC;YAED,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,CAAC,QAAQ,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,GAAG,EAAE,CAAC;gBACR,YAAY,CAAC,QAAQ,CAAC,yBAAyB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YAC5D,CAAC;YAGD,YAAY,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE3D,OAAO,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,EAChC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,GAAG;gBACV,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,KAAmB;QACxC,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAC5C,GAAG,MAAM;YACT,EAAE,EAAE,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;SACvE,CAAC,CAAC,CAAC;QAEJ,MAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE;aAClD,MAAM,EAAE;aACR,MAAM,CAAC,cAAc,CAAC;aACtB,QAAQ,EAAE;aACV,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,KAGtB;QACC,MAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE;aAClD,MAAM,EAAE;aACR,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC;aACzD,OAAO,EAAE,CAAC;IACf,CAAC;CACF,CAAA;AArHY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;IAC/B,WAAA,IAAA,0BAAgB,EAAC,oCAAgB,CAAC,CAAA;qCADuB,gBAAM;QAExB,oBAAU;GAJzC,iBAAiB,CAqH7B"}