"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateAvgHoldingPeriod = calculateAvgHoldingPeriod;
function calculateAvgHoldingPeriod(param) {
    let totalHoldingDays = 0;
    let count = 0;
    for (const record of param) {
        const { openDate, closedDate, status } = record;
        if ((status === 'profit' || status === 'loss') && openDate && closedDate) {
            const open = new Date(openDate);
            const close = new Date(closedDate);
            const holdingDays = (close.getTime() - open.getTime()) / (1000 * 60 * 60 * 24);
            totalHoldingDays += holdingDays;
            count++;
        }
    }
    if (count === 0)
        return 0;
    return Math.ceil(totalHoldingDays / count);
}
//# sourceMappingURL=calculate-avg-holding-period.utll.js.map