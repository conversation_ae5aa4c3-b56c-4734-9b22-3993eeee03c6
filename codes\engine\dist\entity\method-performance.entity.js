"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MethodPerformanceEntity = void 0;
const typeorm_1 = require("typeorm");
let MethodPerformanceEntity = class MethodPerformanceEntity {
    methodId;
    fromDate;
    endDate;
    totalValidTrade;
    totalInvalidTrade;
    averageRewardRiskRatio;
    probability;
    avgOpenPosition;
    avgStopPercent;
    avgProfitPercent;
    avgConsecutiveLoss;
    avgConsecutiveProfit;
    avgHoldingPeriod;
    maxOpenPosition;
    maxStopPercent;
    maxProfitPercent;
    maxConsecutiveLoss;
    maxConsecutiveProfit;
    maxHoldingPeriod;
    cumulativePercentage;
    avgValidTradeByMonth;
    avgValidTradeByDay;
    avgRevenueByTrade;
};
exports.MethodPerformanceEntity = MethodPerformanceEntity;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', nullable: false }),
    __metadata("design:type", String)
], MethodPerformanceEntity.prototype, "methodId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], MethodPerformanceEntity.prototype, "fromDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], MethodPerformanceEntity.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "totalValidTrade", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "totalInvalidTrade", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "averageRewardRiskRatio", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "probability", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "avgOpenPosition", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "avgStopPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "avgProfitPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "avgConsecutiveLoss", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "avgConsecutiveProfit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "avgHoldingPeriod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "maxOpenPosition", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "maxStopPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "maxProfitPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "maxConsecutiveLoss", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "maxConsecutiveProfit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "maxHoldingPeriod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "cumulativePercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "avgValidTradeByMonth", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "avgValidTradeByDay", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Number)
], MethodPerformanceEntity.prototype, "avgRevenueByTrade", void 0);
exports.MethodPerformanceEntity = MethodPerformanceEntity = __decorate([
    (0, typeorm_1.Entity)('method-performance'),
    (0, typeorm_1.Index)(['methodId', 'fromDate', 'endDate'], { unique: true })
], MethodPerformanceEntity);
//# sourceMappingURL=method-performance.entity.js.map