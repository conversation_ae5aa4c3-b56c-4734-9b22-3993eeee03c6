"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const swagger_1 = require("@nestjs/swagger");
const cluster_1 = __importDefault(require("cluster"));
const os_1 = __importDefault(require("os"));
const configurations_1 = __importDefault(require("./configurations"));
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const body_parser_1 = __importDefault(require("body-parser"));
const delay_util_1 = require("./util/delay.util");
async function startApp() {
    const startTime = Date.now();
    console.log(`[${new Date().toISOString()}] Starting application on platform: ${process.platform}, PID: ${process.pid}`);
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const logger = app.get(nest_winston_1.WINSTON_MODULE_NEST_PROVIDER);
    app.useLogger(logger);
    logger.log(`Application created successfully on ${process.platform}`, 'Bootstrap');
    logger.log(`Process ID: ${process.pid}`, 'Bootstrap');
    logger.log(`Node.js version: ${process.version}`, 'Bootstrap');
    logger.log(`Working directory: ${process.cwd()}`, 'Bootstrap');
    app.enableVersioning({
        type: common_1.VersioningType.URI,
        defaultVersion: '1',
    });
    app.use(body_parser_1.default.json({ limit: '10mb' }));
    app.use(body_parser_1.default.urlencoded({ limit: '10mb', extended: true }));
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
    }));
    const swaggerConfig = new swagger_1.DocumentBuilder()
        .setTitle((0, configurations_1.default)('APP_NAME'))
        .setVersion('1.0')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'API Key',
        description: 'Enter your API key as Bearer token',
    })
        .build();
    if ((0, configurations_1.default)('NODE_ENV') === 'development') {
        const document = swagger_1.SwaggerModule.createDocument(app, swaggerConfig);
        swagger_1.SwaggerModule.setup('', app, document);
    }
    const port = (0, configurations_1.default)('PORT') || 3000;
    logger.log(`Starting server on port ${port}`, 'Bootstrap');
    logger.log(`Environment: ${(0, configurations_1.default)('NODE_ENV')}`, 'Bootstrap');
    logger.log(`Engine mode: ${(0, configurations_1.default)('ENGINE_MODE')}`, 'Bootstrap');
    logger.log(`Cluster disabled: ${(0, configurations_1.default)('DISABLE_CLUSTER')}`, 'Bootstrap');
    await app.listen(port);
    const bootDuration = Date.now() - startTime;
    logger.log(`Application started successfully in ${bootDuration}ms`, 'Bootstrap');
    console.log(`[${new Date().toISOString()}] Application is running on port ${port} (${process.platform}, PID: ${process.pid})`);
}
async function startCluster() {
    const allocatedCore = (0, configurations_1.default)('CPU_ALLOCATION')
        ? Math.min(os_1.default.cpus().length, (0, configurations_1.default)('CPU_ALLOCATION'))
        : os_1.default.cpus().length;
    if (cluster_1.default.isPrimary) {
        console.log(`[${new Date().toISOString()}] Cluster primary started on ${process.platform}, PID: ${process.pid}`);
        console.log(`[${new Date().toISOString()}] Total CPU cores: ${os_1.default.cpus().length}, Allocated: ${allocatedCore}`);
        console.log(`[${new Date().toISOString()}] Starting ${allocatedCore} worker(s)...`);
        for (let i = 0; i < allocatedCore; i++) {
            console.log(`[${new Date().toISOString()}] Forking worker ${i + 1}/${allocatedCore}`);
            cluster_1.default.fork({
                CORE_NUMBER: i,
            });
        }
        cluster_1.default.on('online', (worker) => {
            console.log(`[${new Date().toISOString()}] Worker ${worker.process.pid} is online`);
        });
        cluster_1.default.on('exit', (worker, code, signal) => {
            console.log(`[${new Date().toISOString()}] Worker ${worker.process.pid} exited with code ${code} and signal ${signal}`);
            console.log(`[${new Date().toISOString()}] Creating a new worker...`);
            const CORE_NUMBER = process.env.CORE_NUMBER;
            cluster_1.default.fork({
                CORE_NUMBER,
            });
        });
    }
    else {
        let coreNumber = 0;
        const workerId = cluster_1.default.worker?.id || 0;
        for (let i = -(workerId + 1); i < allocatedCore; i += allocatedCore) {
            coreNumber = i;
        }
        console.log(`[${new Date().toISOString()}] Worker ${coreNumber} with PID ${process.pid} starting on ${process.platform}...`);
        const delayMs = Math.round(1000 / allocatedCore);
        console.log(`[${new Date().toISOString()}] Worker ${coreNumber} waiting ${delayMs}ms before startup`);
        await (0, delay_util_1.delay)(delayMs);
        await startApp();
    }
}
console.log(`[${new Date().toISOString()}] Main process starting - Cluster disabled: ${(0, configurations_1.default)('DISABLE_CLUSTER')}`);
if ((0, configurations_1.default)('DISABLE_CLUSTER') === 'true') {
    console.log(`[${new Date().toISOString()}] Starting in single process mode`);
    startApp();
}
else {
    console.log(`[${new Date().toISOString()}] Starting in cluster mode`);
    startCluster();
}
//# sourceMappingURL=main.js.map