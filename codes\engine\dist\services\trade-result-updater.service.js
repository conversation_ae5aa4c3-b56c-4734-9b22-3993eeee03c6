"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeResultUpdaterService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const trade_result_service_1 = require("./trade-result.service");
const method_service_1 = require("./method.service");
let TradeResultUpdaterService = class TradeResultUpdaterService {
    logger;
    tradeResultService;
    methodService;
    constructor(logger, tradeResultService, methodService) {
        this.logger = logger;
        this.tradeResultService = tradeResultService;
        this.methodService = methodService;
    }
    async updateTradeResult() {
        try {
            const activeResults = await this.tradeResultService.getActiveResults();
            for (const result of activeResults) {
                const methodResult = await this.methodService.getResultById(result.id);
                if (!methodResult) {
                    if (result.status === 'pending') {
                        await this.tradeResultService.updateStatus({
                            ...result,
                            status: 'canceled',
                        });
                    }
                    else {
                        await this.tradeResultService.updateStatus({
                            ...result,
                            status: 'invalid',
                        });
                    }
                    continue;
                }
                await this.tradeResultService.updateStatus({
                    ...result,
                    openDate: methodResult.openDate,
                    closedDate: methodResult.closedDate,
                    status: methodResult.status,
                });
            }
        }
        catch (error) {
            this.logger.error('Failed to run update', (0, log_detail_util_1.logDetail)({
                class: 'TradeResultUpdaterService',
                function: 'runUpdate',
                error,
            }));
            return;
        }
    }
};
exports.TradeResultUpdaterService = TradeResultUpdaterService;
exports.TradeResultUpdaterService = TradeResultUpdaterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        trade_result_service_1.TradeResultService,
        method_service_1.MethodService])
], TradeResultUpdaterService);
//# sourceMappingURL=trade-result-updater.service.js.map