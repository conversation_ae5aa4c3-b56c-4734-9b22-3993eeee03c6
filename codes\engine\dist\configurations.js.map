{"version": 3, "file": "configurations.js", "sourceRoot": "", "sources": ["../src/configurations.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,+CAAiC;AAEjC,uCAAyB;AACzB,wFAAmF;AACnF,wEAAoE;AAEpE,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAC;AACvD,MAAM,WAAW,GAAG,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,MAAM,CAAC;AAChF,MAAM,aAAa,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC;AAEvC,MAAM,cAAc,GAAG,IAAI,uDAAyB,CAAC,IAAI,wCAAkB,EAAE,CAAC,CAAC;AAG/E,MAAM,oBAAoB,GAAG,MAAM,CAAC,mBAAmB,CACrD,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CACtC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;IAChB,MAAM,IAAI,GAAI,cAAsB,CAAC,IAAI,CAAC,CAAC;IAC3C,OAAO,OAAO,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,aAAa,CAAC;AAC9D,CAAC,CAAC,CAAC;AAEH,kBAAe,CAAC,GAAW,EAAE,EAAE;IAC7B,IAAI,GAAG,GAAQ,EAAE,CAAC;IAElB,IAAI,CAAC;QACH,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CACT,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,gCAAgC,WAAW,EAAE,CAC1E,CAAC;QACF,OAAO,CAAC,GAAG,CACT,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,mCAAmC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,OAAO,CAC9F,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CACX,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,sCAAsC,WAAW,EAAE,EAC/E,KAAK,CACN,CAAC;QACF,OAAO,CAAC,GAAG,CACT,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,sCAAsC,CACnE,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,GAAG;QACX,GAAG,GAAG;QACN,QAAQ,EAAE,uBAAuB;QACjC,cAAc,EAAE,sCAAsC;QACtD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI;QAC9B,QAAQ;QACR,sBAAsB,EAAE,GAAG,CAAC,sBAAsB,KAAK,MAAM;QAC7D,wBAAwB,EAAE,GAAG,CAAC,wBAAwB,KAAK,MAAM;QACjE,oBAAoB,EAAE,GAAG,CAAC,oBAAoB;QAC9C,wBAAwB,EAAE,GAAG,CAAC,wBAAwB;QACtD,SAAS,EAAE,GAAG,CAAC,SAAS,IAAI,OAAO;QACnC,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,MAAM;QAC9B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC;QACtC,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC;QACxC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,SAAS;QACvD,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,GAAG;QAC/C,0BAA0B,EACxB,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,aAAa;QACzD,UAAU,EAAE,aAAa;QACzB,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;QAC5C,uBAAuB,EAAE,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,IAAI;QACpE,yBAAyB,EAAE,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,GAAG;QACvE,SAAS,EAAE,CAAC,GAAG,CAAC,SAAS,IAAI,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;QAClE,OAAO,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9C,sBAAsB,EAAE,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,EAAE;QAChE,sBAAsB,EAAE,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,EAAE;QAChE,2BAA2B,EAAE,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,IAAI,CAAC;QACzE,sBAAsB,EAAE,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,EAAE;QAChE,yBAAyB,EAAE,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,CAAC;QACrE,WAAW,EAAE,CAAC,GAAG,CAAC,WAAW,IAAI,gBAAgB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7D,qBAAqB,EAAE,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE;QAC9D,iCAAiC,EAC/B,MAAM,CAAC,GAAG,CAAC,iCAAiC,CAAC,IAAI,GAAG;QACtD,6BAA6B,EAAE,CAC7B,GAAG,CAAC,6BAA6B,IAAI,OAAO,CAC7C;aACE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,kBAAkB,EAAE,GAAG,CAAC,kBAAkB,IAAI,IAAI;QAClD,wBAAwB,EAAE,CAAC,GAAG,CAAC,wBAAwB,IAAI,IAAI,CAAC;aAC7D,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,oBAAoB,EAAE,CAAC,GAAG,CAAC,oBAAoB,IAAI,KAAK,CAAC;aACtD,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,MAAM,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,iBAAiB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QACpD,WAAW,EAAE,CAAC,GAAG,CAAC,WAAW,IAAI,YAAY,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QACzD,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI;QAC9C,6BAA6B,EAAE,CAC7B,GAAG,CAAC,6BAA6B,IAAI,aAAa,CACnD;aACE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,6BAA6B,EAAE,CAC7B,GAAG,CAAC,6BAA6B,IAAI,aAAa,CACnD;aACE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,qCAAqC,EAAE,CACrC,GAAG,CAAC,qCAAqC,IAAI,OAAO,CACrD;aACE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACxC,2BAA2B,EAAE,CAAC,GAAG,CAAC,2BAA2B,IAAI,OAAO,CAAC;aACtE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACxC,2BAA2B,EAAE,CAAC,GAAG,CAAC,2BAA2B,IAAI,OAAO,CAAC;aACtE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACxC,oBAAoB;QACpB,aAAa,EAAE,CAAC,GAAG,CAAC,aAAa,IAAI,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9D,QAAQ,EAAE;YACR,IAAI,EAAE,GAAG,CAAC,OAAO;YACjB,IAAI,EAAE,GAAG,CAAC,OAAO;YACjB,IAAI,EAAE,GAAG,CAAC,OAAO;YACjB,QAAQ,EAAE,GAAG,CAAC,OAAO;YACrB,QAAQ,EAAE,GAAG,CAAC,WAAW;YACzB,QAAQ,EAAE,GAAG,CAAC,OAAO;YACrB,QAAQ,EAAE,CAAC,SAAS,GAAG,uBAAuB,CAAC;YAC/C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;YAClD,GAAG,EACD,GAAG,CAAC,cAAc,KAAK,MAAM;gBAC3B,CAAC,CAAC;oBACE,kBAAkB,EAAE,KAAK;oBACzB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;iBAC1C;gBACH,CAAC,CAAC,KAAK;SACY;KAC1B,CAAC;IAGF,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;QACrC,OAAO,CAAC,IAAI,CACV,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,wBAAwB,GAAG,gCAAgC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CACtI,CAAC;IACJ,CAAC;IAED,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;QACtE,OAAO,CAAC,GAAG,CACT,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,mBAAmB,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CACnF,CAAC;IACJ,CAAC;IAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC,CAAC"}