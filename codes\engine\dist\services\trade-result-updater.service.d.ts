import { Logger } from 'winston';
import { TradeResultService } from './trade-result.service';
import { MethodService } from './method.service';
export declare class TradeResultUpdaterService {
    private readonly logger;
    private readonly tradeResultService;
    private readonly methodService;
    constructor(logger: Logger, tradeResultService: TradeResultService, methodService: MethodService);
    updateTradeResult(): Promise<void>;
}
