import { Logger } from 'winston';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { Historical } from 'src/interface/historical.interface';
import { AmendOrderParamsV5, CancelOrderParamsV5, OrderParamsV5, SetLeverageParamsV5, GetAccountOrdersParamsV5, GetAccountHistoricOrdersParamsV5, AccountOrderV5 } from 'bybit-api';
import { Instrument } from 'src/interface/instrument.interface';
import { ExternalAdapterService } from './external-adapter.service';
import { TradeResult } from 'src/interface/trade-result.interface';
export declare class AdapterService {
    private readonly logger;
    private readonly externalAdapterService;
    private readonly isExternalEnabled;
    constructor(logger: Logger, externalAdapterService: ExternalAdapterService);
    switchToHedgeMode(): Promise<any>;
    setLeverage(param: SetLeverageParamsV5): Promise<any>;
    getWalletBalance(): Promise<number>;
    placeOrder(param: OrderParamsV5): Promise<any>;
    amendOrder(param: TradeResult | AmendOrderParamsV5): Promise<any>;
    cancelOrder(param: TradeResult | CancelOrderParamsV5): Promise<any>;
    getActiveOrders(param: GetAccountOrdersParamsV5): Promise<AccountOrderV5[]>;
    getOrderHistory(param: GetAccountHistoricOrdersParamsV5): Promise<AccountOrderV5[]>;
    fetchBybitHistorical(param: GetHistoricalDto): Promise<Historical[]>;
    fetchBybitInstrument(param?: GetInstrumentDto): Promise<Instrument[]>;
}
