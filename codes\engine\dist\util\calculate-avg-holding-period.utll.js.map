{"version": 3, "file": "calculate-avg-holding-period.utll.js", "sourceRoot": "", "sources": ["../../src/util/calculate-avg-holding-period.utll.ts"], "names": [], "mappings": ";;AAEA,8DAqBC;AArBD,SAAgB,yBAAyB,CAAC,KAAqB;IAC7D,IAAI,gBAAgB,GAAG,CAAC,CAAC;IACzB,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,MAAM,MAAM,IAAI,KAAK,EAAE,CAAC;QAC3B,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;QAGhD,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,MAAM,CAAC,IAAI,QAAQ,IAAI,UAAU,EAAE,CAAC;YACzE,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;YACnC,MAAM,WAAW,GACf,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAE7D,gBAAgB,IAAI,WAAW,CAAC;YAChC,KAAK,EAAE,CAAC;QACV,CAAC;IACH,CAAC;IAED,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,CAAC,CAAC;IAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC;AAC7C,CAAC"}