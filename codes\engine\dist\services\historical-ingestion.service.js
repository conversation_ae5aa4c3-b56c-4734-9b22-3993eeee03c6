"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoricalIngestionService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const log_detail_util_1 = require("../util/log-detail.util");
const configurations_1 = __importDefault(require("../configurations"));
const adapter_service_1 = require("./adapter.service");
const to_milliseconds_util_1 = require("../util/to-milliseconds.util");
const historical_cache_service_1 = require("./historical-cache.service");
const historical_service_1 = require("./historical.service");
const instrument_service_1 = require("./instrument.service");
const suffle_array_util_1 = require("../util/suffle-array.util");
const get_symbols_slice_util_1 = require("../util/get-symbols-slice.util");
const method_status_service_1 = require("./method-status.service");
let HistoricalIngestionService = class HistoricalIngestionService {
    logger;
    historicalService;
    methodStatusService;
    adapterService;
    historicalCacheService;
    instrumentService;
    constructor(logger, historicalService, methodStatusService, adapterService, historicalCacheService, instrumentService) {
        this.logger = logger;
        this.historicalService = historicalService;
        this.methodStatusService = methodStatusService;
        this.adapterService = adapterService;
        this.historicalCacheService = historicalCacheService;
        this.instrumentService = instrumentService;
    }
    async ingestHistorical(interval) {
        const startTime = Date.now();
        const engineMode = (0, configurations_1.default)('ENGINE_MODE');
        const disableCluster = (0, configurations_1.default)('DISABLE_CLUSTER');
        const intervals = interval ? [interval] : (0, configurations_1.default)('INTERVALS');
        const executionInterval = (0, configurations_1.default)('EXECUTION_INTERVAL');
        this.logger.info(`Starting historical ingestion`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalIngestionService',
            function: 'ingestHistorical',
            param: {
                engineMode,
                disableCluster,
                intervals,
                executionInterval,
                platform: process.platform,
                pid: process.pid,
                specificInterval: interval,
            },
        }));
        if (engineMode === 'worker') {
            this.logger.info(`Running in worker mode`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'ingestHistorical',
                param: { engineMode, disableCluster },
            }));
            let symbols = await this.instrumentService.getSymbols();
            this.logger.info(`Retrieved ${symbols.length} symbols from instrument service`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'ingestHistorical',
                param: { totalSymbols: symbols.length },
            }));
            symbols =
                disableCluster === 'false'
                    ? (0, suffle_array_util_1.shuffleArray)((0, get_symbols_slice_util_1.getSymbolsSlice)(symbols))
                    : (0, suffle_array_util_1.shuffleArray)(symbols);
            this.logger.info(`Processed symbols for worker mode`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'ingestHistorical',
                param: {
                    finalSymbolCount: symbols.length,
                    disableCluster,
                    symbols: symbols.slice(0, 10),
                },
            }));
            const intervalIds = [];
            for (const symbol of symbols) {
                for (const item of intervals) {
                    intervalIds.push(`${symbol}-${item}`);
                }
            }
            this.logger.info(`Generated ${intervalIds.length} interval IDs for processing`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'ingestHistorical',
                param: {
                    totalIntervalIds: intervalIds.length,
                    sampleIds: intervalIds.slice(0, 10),
                },
            }));
            await this.ingestSymbols(symbols, [
                ...new Set(intervalIds.map((item) => item)),
            ]);
            const totalDuration = Date.now() - startTime;
            this.logger.info(`Completed historical ingestion in worker mode`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'ingestHistorical',
                param: {
                    totalDuration,
                    symbolsProcessed: symbols.length,
                    intervalIdsProcessed: intervalIds.length,
                },
            }));
        }
        if (engineMode === 'service') {
            const methodStatuses = await this.methodStatusService.getAllMethodStatus();
            let symbols = [...new Set(methodStatuses.map((item) => item.symbol))];
            symbols =
                disableCluster === 'false'
                    ? (0, suffle_array_util_1.shuffleArray)((0, get_symbols_slice_util_1.getSymbolsSlice)(symbols))
                    : (0, suffle_array_util_1.shuffleArray)(symbols);
            const intervalIds = [];
            for (const item of methodStatuses) {
                intervalIds.push(`${item.symbol}-${item.interval}`);
                intervalIds.push(`${item.symbol}-${executionInterval}`);
            }
            await this.ingestSymbols(symbols, [
                ...new Set(intervalIds.map((item) => item)),
            ]);
        }
    }
    async ingestSymbols(symbols, intervalIds) {
        const startTime = Date.now();
        this.logger.info(`Starting symbol ingestion`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalIngestionService',
            function: 'ingestSymbols',
            param: {
                symbolCount: symbols.length,
                intervalIdCount: intervalIds.length,
                symbols: symbols.slice(0, 5),
            },
        }));
        let processedSymbols = 0;
        for (const symbol of symbols) {
            const symbolStartTime = Date.now();
            const intervals = intervalIds
                .filter((item) => item.split('-')[0] === symbol)
                .map((item) => item.split('-')[1]);
            this.logger.info(`Processing symbol ${++processedSymbols}/${symbols.length}: ${symbol}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'ingestSymbols',
                param: {
                    symbol,
                    symbolIndex: processedSymbols,
                    totalSymbols: symbols.length,
                    intervals,
                    intervalCount: intervals.length,
                },
            }));
            let processedIntervals = 0;
            for (const interval of intervals) {
                const intervalStartTime = Date.now();
                this.logger.info(`Ingesting ${symbol} ${interval} (${++processedIntervals}/${intervals.length})`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalIngestionService',
                    function: 'ingestSymbols',
                    param: {
                        symbol,
                        interval,
                        intervalIndex: processedIntervals,
                        totalIntervals: intervals.length,
                    },
                }));
                try {
                    await this.process(symbol, interval);
                    const intervalDuration = Date.now() - intervalStartTime;
                    this.logger.info(`Completed ingesting ${symbol} ${interval} in ${intervalDuration}ms`, (0, log_detail_util_1.logDetail)({
                        class: 'HistoricalIngestionService',
                        function: 'ingestSymbols',
                        param: {
                            symbol,
                            interval,
                            duration: intervalDuration,
                        },
                    }));
                }
                catch (error) {
                    const intervalDuration = Date.now() - intervalStartTime;
                    this.logger.error(`Failed to ingest ${symbol} ${interval} after ${intervalDuration}ms`, (0, log_detail_util_1.logDetail)({
                        class: 'HistoricalIngestionService',
                        function: 'ingestSymbols',
                        error,
                        param: {
                            symbol,
                            interval,
                            duration: intervalDuration,
                        },
                    }));
                }
            }
            const symbolDuration = Date.now() - symbolStartTime;
            this.logger.info(`Completed processing symbol ${symbol} in ${symbolDuration}ms`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'ingestSymbols',
                param: {
                    symbol,
                    intervalsProcessed: intervals.length,
                    duration: symbolDuration,
                },
            }));
        }
        const totalDuration = Date.now() - startTime;
        this.logger.info(`Completed all symbol ingestion`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalIngestionService',
            function: 'ingestSymbols',
            param: {
                totalSymbols: symbols.length,
                totalDuration,
                averageTimePerSymbol: Math.round(totalDuration / symbols.length),
            },
        }));
    }
    async process(symbol, interval) {
        const processStartTime = Date.now();
        this.logger.debug(`Starting process for ${symbol}-${interval}`, (0, log_detail_util_1.logDetail)({
            class: 'HistoricalIngestionService',
            function: 'process',
            param: {
                symbol,
                interval,
                platform: process.platform,
                pid: process.pid,
            },
        }));
        try {
            const startDate = new Date(0);
            const endDate = new Date();
            const defaultLimit = (0, configurations_1.default)('DEFAULT_LIMIT');
            const startTime = startDate.getTime();
            const endTime = endDate.getTime();
            const timeRange = (0, to_milliseconds_util_1.toMiliseconds)(interval);
            this.logger.debug(`Process configuration`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'process',
                param: {
                    symbol,
                    interval,
                    defaultLimit,
                    timeRange,
                    startDate: startDate.toISOString(),
                    endDate: endDate.toISOString(),
                },
            }));
            const instrument = await this.instrumentService.getInstrument({
                symbol,
            });
            if (!instrument) {
                this.logger.warn(`No instrument found for symbol: ${symbol}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalIngestionService',
                    function: 'process',
                    param: { symbol, interval },
                }));
                return;
            }
            this.logger.debug(`Instrument found for ${symbol}`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'process',
                param: {
                    symbol,
                    interval,
                    listedTime: instrument[0].listedTime,
                },
            }));
            const listedTime = new Date(Number(instrument[0].listedTime)).getTime();
            const rowLengthTarget = Math.ceil((endTime - listedTime) / (0, to_milliseconds_util_1.toMiliseconds)(interval));
            this.logger.debug(`Calculated processing parameters`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'process',
                param: {
                    symbol,
                    interval,
                    listedTime: new Date(listedTime).toISOString(),
                    rowLengthTarget,
                    timeSpan: endTime - listedTime,
                },
            }));
            let batchCount = 0;
            const totalBatches = Math.ceil((endTime - startTime) / (timeRange * defaultLimit));
            this.logger.debug(`Starting batch processing`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'process',
                param: {
                    symbol,
                    interval,
                    totalBatches,
                    batchSize: defaultLimit,
                },
            }));
            for (let i = endTime; i > startTime; i -= timeRange * defaultLimit) {
                const batchStartTime = Date.now();
                batchCount++;
                const startScan = new Date(i - timeRange * defaultLimit);
                const endScan = new Date(i);
                this.logger.debug(`Processing batch ${batchCount}/${totalBatches} for ${symbol}-${interval}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalIngestionService',
                    function: 'process',
                    param: {
                        symbol,
                        interval,
                        batchIndex: batchCount,
                        totalBatches,
                        startScan: startScan.toISOString(),
                        endScan: endScan.toISOString(),
                    },
                }));
                let existHistoricalCacheCount = await this.historicalCacheService.countHistorical(symbol, interval, startScan, endScan);
                let existHistoricalCount = await this.historicalService.countHistorical(symbol, interval, startScan, endScan);
                this.logger.debug(`Counts - Cache: ${existHistoricalCacheCount}, DB: ${existHistoricalCount}, Default Limit: ${defaultLimit}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalIngestionService',
                    function: 'process',
                    param: {
                        symbol,
                        interval,
                        batchIndex: batchCount,
                        existHistoricalCacheCount,
                        existHistoricalCount,
                        defaultLimit,
                    },
                }));
                if (existHistoricalCacheCount === defaultLimit &&
                    existHistoricalCount < defaultLimit) {
                    this.logger.info(`Cache has more data (${existHistoricalCacheCount}) than DB (${existHistoricalCount}). Syncing cache data for current batch to DB.`, (0, log_detail_util_1.logDetail)({
                        class: 'HistoricalIngestionService',
                        function: 'process',
                        param: {
                            symbol,
                            interval,
                            cacheCount: existHistoricalCacheCount,
                            dbCount: existHistoricalCount,
                            batchIndex: batchCount,
                            startScan: startScan.toISOString(),
                            endScan: endScan.toISOString(),
                        },
                    }));
                    const cacheData = await this.historicalCacheService.getHistorical({
                        symbol,
                        interval,
                        sort: 'DESC',
                        limit: defaultLimit,
                        start: startScan,
                        end: endScan,
                    });
                    if (cacheData.length === defaultLimit) {
                        await this.historicalService.insertHistorical(cacheData);
                        this.logger.info(`Successfully synced ${cacheData.length} records from cache to DB for batch ${batchCount}`, (0, log_detail_util_1.logDetail)({
                            class: 'HistoricalIngestionService',
                            function: 'process',
                            param: {
                                symbol,
                                interval,
                                batchIndex: batchCount,
                                recordsSynced: cacheData.length,
                                startScan: startScan.toISOString(),
                                endScan: endScan.toISOString(),
                            },
                        }));
                        existHistoricalCount = defaultLimit;
                        continue;
                    }
                }
                if (existHistoricalCount === defaultLimit &&
                    existHistoricalCacheCount < defaultLimit) {
                    this.logger.info(`DB has more data (${existHistoricalCount}) than cache (${existHistoricalCacheCount}). Syncing DB data for current batch to cache.`, (0, log_detail_util_1.logDetail)({
                        class: 'HistoricalIngestionService',
                        function: 'process',
                        param: {
                            symbol,
                            interval,
                            dbCount: existHistoricalCount,
                            cacheCount: existHistoricalCacheCount,
                            batchIndex: batchCount,
                            startScan: startScan.toISOString(),
                            endScan: endScan.toISOString(),
                        },
                    }));
                    const dbData = await this.historicalService.getHistorical({
                        symbol,
                        interval,
                        sort: 'DESC',
                        limit: defaultLimit,
                        start: startScan,
                        end: endScan,
                    });
                    if (dbData.length === defaultLimit) {
                        await this.historicalCacheService.insertHistorical(dbData);
                        this.logger.info(`Successfully synced ${dbData.length} records from DB to cache for batch ${batchCount}`, (0, log_detail_util_1.logDetail)({
                            class: 'HistoricalIngestionService',
                            function: 'process',
                            param: {
                                symbol,
                                interval,
                                batchIndex: batchCount,
                                recordsSynced: dbData.length,
                                startScan: startScan.toISOString(),
                                endScan: endScan.toISOString(),
                            },
                        }));
                        existHistoricalCacheCount = defaultLimit;
                        continue;
                    }
                }
                if (existHistoricalCount === defaultLimit &&
                    existHistoricalCacheCount === defaultLimit) {
                    const existHistoricalCacheFullCount = await this.historicalCacheService.countHistorical(symbol, interval);
                    const existHistoricalFullCount = await this.historicalService.countHistorical(symbol, interval);
                    if (existHistoricalCacheFullCount >= rowLengthTarget &&
                        existHistoricalFullCount >= rowLengthTarget) {
                        this.logger.info(`Both cache and DB have reached target count. Breaking batch loop.`, (0, log_detail_util_1.logDetail)({
                            class: 'HistoricalIngestionService',
                            function: 'process',
                            param: {
                                symbol,
                                interval,
                                batchIndex: batchCount,
                                cacheCount: existHistoricalCacheFullCount,
                                dbCount: existHistoricalFullCount,
                                targetCount: rowLengthTarget,
                            },
                        }));
                        break;
                    }
                    else {
                        continue;
                    }
                }
                this.logger.debug(`Fetching new data for batch ${batchCount}`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalIngestionService',
                    function: 'process',
                    param: {
                        symbol,
                        interval,
                        batchIndex: batchCount,
                        startScan: startScan.toISOString(),
                        endScan: endScan.toISOString(),
                        limit: defaultLimit,
                    },
                }));
                const fetchStartTime = Date.now();
                const newHistorical = await this.adapterService.fetchBybitHistorical({
                    symbol,
                    interval,
                    sort: 'DESC',
                    limit: defaultLimit,
                    start: startScan,
                    end: endScan,
                });
                const fetchDuration = Date.now() - fetchStartTime;
                this.logger.debug(`Fetched ${newHistorical.length} new records in ${fetchDuration}ms`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalIngestionService',
                    function: 'process',
                    param: {
                        symbol,
                        interval,
                        batchIndex: batchCount,
                        recordsFetched: newHistorical.length,
                        fetchDuration,
                    },
                }));
                const dbInsertStartTime = Date.now();
                await this.historicalService.insertHistorical(newHistorical);
                const dbInsertDuration = Date.now() - dbInsertStartTime;
                this.logger.debug(`Inserted ${newHistorical.length} records to DB in ${dbInsertDuration}ms`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalIngestionService',
                    function: 'process',
                    param: {
                        symbol,
                        interval,
                        batchIndex: batchCount,
                        recordsInserted: newHistorical.length,
                        dbInsertDuration,
                    },
                }));
                const cacheInsertStartTime = Date.now();
                await this.historicalCacheService.insertHistorical(newHistorical);
                const cacheInsertDuration = Date.now() - cacheInsertStartTime;
                this.logger.debug(`Inserted ${newHistorical.length} records to cache in ${cacheInsertDuration}ms`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalIngestionService',
                    function: 'process',
                    param: {
                        symbol,
                        interval,
                        batchIndex: batchCount,
                        recordsInserted: newHistorical.length,
                        cacheInsertDuration,
                    },
                }));
                if (newHistorical.length < defaultLimit) {
                    this.logger.info(`No more data available from Bybit for ${symbol}-${interval}`, (0, log_detail_util_1.logDetail)({
                        class: 'HistoricalIngestionService',
                        function: 'process',
                        param: {
                            symbol,
                            interval,
                            batchIndex: batchCount,
                            finalCount: existHistoricalCacheCount,
                        },
                    }));
                    break;
                }
                const batchDuration = Date.now() - batchStartTime;
                this.logger.debug(`Completed batch ${batchCount}/${totalBatches} in ${batchDuration}ms`, (0, log_detail_util_1.logDetail)({
                    class: 'HistoricalIngestionService',
                    function: 'process',
                    param: {
                        symbol,
                        interval,
                        batchIndex: batchCount,
                        totalBatches,
                        batchDuration,
                        recordsProcessed: newHistorical.length,
                    },
                }));
            }
            const totalProcessDuration = Date.now() - processStartTime;
            this.logger.info(`Completed processing ${symbol}-${interval} in ${totalProcessDuration}ms`, (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'process',
                param: {
                    symbol,
                    interval,
                    totalBatches: batchCount,
                    totalDuration: totalProcessDuration,
                    averageBatchTime: Math.round(totalProcessDuration / batchCount),
                },
            }));
            return;
        }
        catch (error) {
            const errorDuration = Date.now() - processStartTime;
            this.logger.error('Failed to process historical data', (0, log_detail_util_1.logDetail)({
                class: 'HistoricalIngestionService',
                function: 'process',
                param: {
                    symbol,
                    interval,
                    duration: errorDuration,
                    platform: process.platform,
                },
                error,
            }));
            return;
        }
    }
};
exports.HistoricalIngestionService = HistoricalIngestionService;
exports.HistoricalIngestionService = HistoricalIngestionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        historical_service_1.HistoricalService,
        method_status_service_1.MethodStatusService,
        adapter_service_1.AdapterService,
        historical_cache_service_1.HistoricalCacheService,
        instrument_service_1.InstrumentService])
], HistoricalIngestionService);
//# sourceMappingURL=historical-ingestion.service.js.map