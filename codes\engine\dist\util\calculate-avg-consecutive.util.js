"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateAvgConsecutive = calculateAvgConsecutive;
function calculateAvgConsecutive(param, target) {
    const consecutiveCount = [];
    let consecutive = 0;
    for (const result of param.sort((a, b) => {
        const closedDateA = a.closedDate || a.date;
        const closedDateB = b.closedDate || b.date;
        return closedDateA.getTime() - closedDateB.getTime();
    })) {
        if (result.status === target) {
            consecutive++;
        }
        else {
            if (consecutive > 0) {
                consecutiveCount.push(consecutive);
            }
            consecutive = 0;
        }
    }
    if (consecutive > 0) {
        consecutiveCount.push(consecutive);
    }
    const total = consecutiveCount.reduce((acc, cur) => acc + cur, 0);
    const avg = consecutiveCount.length > 0 ? total / consecutiveCount.length : 0;
    return Math.ceil(avg);
}
//# sourceMappingURL=calculate-avg-consecutive.util.js.map