"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AmendOrderDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class AmendOrderDto {
    category;
    symbol;
    orderId;
    orderLinkId;
    orderIv;
    triggerPrice;
    qty;
    price;
    takeProfit;
    stopLoss;
    tpTriggerBy;
    slTriggerBy;
    triggerBy;
    tpLimitPrice;
    slLimitPrice;
}
exports.AmendOrderDto = AmendOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Product category',
        example: 'linear',
        enum: ['spot', 'linear', 'inverse', 'option'],
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['spot', 'linear', 'inverse', 'option']),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Symbol name',
        example: 'BTCUSDT',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "symbol", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Order ID. Either orderId or orderLinkId is required',
        example: '1234567890',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User customised order ID. Either orderId or orderLinkId is required',
        example: 'my-order-001',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "orderLinkId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Implied volatility',
        example: '0.1',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "orderIv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trigger price',
        example: '51000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "triggerPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Order quantity',
        example: '0.02',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "qty", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Order price',
        example: '51000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Take profit price',
        example: '55000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "takeProfit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Stop loss price',
        example: '45000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "stopLoss", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Take profit trigger price type',
        example: 'LastPrice',
        enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['LastPrice', 'IndexPrice', 'MarkPrice']),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "tpTriggerBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Stop loss trigger price type',
        example: 'LastPrice',
        enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['LastPrice', 'IndexPrice', 'MarkPrice']),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "slTriggerBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trigger price type',
        example: 'LastPrice',
        enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['LastPrice', 'IndexPrice', 'MarkPrice']),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "triggerBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Take profit limit price',
        example: '54000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "tpLimitPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Stop loss limit price',
        example: '46000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AmendOrderDto.prototype, "slLimitPrice", void 0);
//# sourceMappingURL=amend-order.dto.js.map