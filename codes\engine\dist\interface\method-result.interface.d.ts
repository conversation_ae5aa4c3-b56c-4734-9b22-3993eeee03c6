export interface MethodResult {
    id?: string;
    interval: string;
    symbol: string;
    orderType: string;
    methodId: string;
    date: Date;
    entry: number;
    stopLoss: number;
    takeProfit: number;
    stopPercent: number;
    profitPercent: number;
    expiryDate: Date;
    openDate?: Date;
    closedDate?: Date;
    status: 'pending' | 'expired' | 'open' | 'profit' | 'loss' | 'invalid' | 'canceled';
    action?: 'place' | 'amend';
}
