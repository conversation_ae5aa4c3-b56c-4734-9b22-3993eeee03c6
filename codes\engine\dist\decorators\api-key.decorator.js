"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyAuth = ApiKeyAuth;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const api_key_guard_1 = require("../guards/api-key.guard");
function ApiKeyAuth() {
    return (0, common_1.applyDecorators)((0, common_1.UseGuards)(api_key_guard_1.ApiKeyGuard), (0, swagger_1.ApiBearerAuth)());
}
//# sourceMappingURL=api-key.decorator.js.map