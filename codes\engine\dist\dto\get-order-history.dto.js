"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetOrderHistoryDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class GetOrderHistoryDto {
    category;
    symbol;
    baseCoin;
    settleCoin;
    orderId;
    orderLinkId;
    orderStatus;
    orderFilter;
    limit;
    cursor;
}
exports.GetOrderHistoryDto = GetOrderHistoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Product category',
        example: 'linear',
        enum: ['spot', 'linear', 'inverse', 'option'],
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['spot', 'linear', 'inverse', 'option']),
    __metadata("design:type", String)
], GetOrderHistoryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Symbol name. If not passed, returns orders for all symbols',
        example: 'BTCUSDT',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetOrderHistoryDto.prototype, "symbol", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Base coin. For option only',
        example: 'BTC',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetOrderHistoryDto.prototype, "baseCoin", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Settle coin. For linear & inverse only',
        example: 'USDT',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetOrderHistoryDto.prototype, "settleCoin", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Order ID',
        example: '1234567890',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetOrderHistoryDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User customised order ID',
        example: 'my-order-001',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetOrderHistoryDto.prototype, "orderLinkId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Order status',
        example: 'Filled',
        enum: [
            'Created',
            'New',
            'Rejected',
            'PartiallyFilled',
            'PartiallyFilledCanceled',
            'Filled',
            'Cancelled',
            'Untriggered',
            'Triggered',
            'Deactivated',
            'Active'
        ],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)([
        'Created',
        'New',
        'Rejected',
        'PartiallyFilled',
        'PartiallyFilledCanceled',
        'Filled',
        'Cancelled',
        'Untriggered',
        'Triggered',
        'Deactivated',
        'Active'
    ]),
    __metadata("design:type", String)
], GetOrderHistoryDto.prototype, "orderStatus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Order filter',
        example: 'Order',
        enum: ['Order', 'tpslOrder', 'StopOrder'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['Order', 'tpslOrder', 'StopOrder']),
    __metadata("design:type", String)
], GetOrderHistoryDto.prototype, "orderFilter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Limit for data size per page. [1, 50]. Default: 20',
        example: 20,
        minimum: 1,
        maximum: 50,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Transform)(({ value }) => Number(value)),
    __metadata("design:type", Number)
], GetOrderHistoryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Cursor. Use the nextPageCursor token from the response to retrieve the next page of the result set',
        example: 'cursor_token_here',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetOrderHistoryDto.prototype, "cursor", void 0);
//# sourceMappingURL=get-order-history.dto.js.map