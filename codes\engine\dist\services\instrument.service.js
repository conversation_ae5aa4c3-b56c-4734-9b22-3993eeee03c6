"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InstrumentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const nest_winston_1 = require("nest-winston");
const configurations_1 = __importDefault(require("../configurations"));
const instrument_entity_1 = require("../entity/instrument.entity");
const delay_util_1 = require("../util/delay.util");
const log_detail_util_1 = require("../util/log-detail.util");
const typeorm_2 = require("typeorm");
const winston_1 = require("winston");
let InstrumentService = class InstrumentService {
    InstrumentsRepository;
    logger;
    constructor(InstrumentsRepository, logger) {
        this.InstrumentsRepository = InstrumentsRepository;
        this.logger = logger;
    }
    async getMinOfMaxLeverage() {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument')
                .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
                .select('MIN(leverageFilter.maxLeverage)');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.min ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getMinOfMaxLeverage',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getMinOfMaxLeverage();
        }
    }
    async getAvgOfMaxLeverage() {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument')
                .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
                .select('AVG(leverageFilter.maxLeverage)');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.avg ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getAvgOfMaxLeverage',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getAvgOfMaxLeverage();
        }
    }
    async getMaxLeverage(symbol) {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument')
                .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
                .where('instrument.symbol = :symbol', { symbol })
                .select('leverageFilter.maxLeverage');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.leverageFilter_maxLeverage ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getMaxLeverage',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getMaxLeverage(symbol);
        }
    }
    async getMaxFundingRate() {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument').select('MAX(instrument.upperFundingRate)');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.max ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getMaxFundingRate',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getMaxFundingRate();
        }
    }
    async getAvgFundingRate() {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument').select('AVG(instrument.upperFundingRate)');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.avg ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getAvgFundingRate',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getAvgFundingRate();
        }
    }
    async getMinFundingInterval() {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument').select('MIN(instrument.fundingInterval)');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.min ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getMaxFundingInterval',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getMinFundingInterval();
        }
    }
    async getAvgFundingInterval() {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument').select('AVG(instrument.fundingInterval)');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.avg ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getAvgFundingInterval',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getAvgFundingInterval();
        }
    }
    async getMinOrderQty(symbol) {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument')
                .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter')
                .where('instrument.symbol = :symbol', { symbol })
                .select('lotSizeFilter.minOrderQty');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.lotSizeFilter_minOrderQty ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getMinOrderQty',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getMinOrderQty(symbol);
        }
    }
    async getMaxOrderQty(symbol) {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument')
                .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter')
                .where('instrument.symbol = :symbol', { symbol })
                .select('lotSizeFilter.maxOrderQty');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.lotSizeFilter_maxOrderQty ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getMaxOrderQty',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getMaxOrderQty(symbol);
        }
    }
    async getQtyStep(symbol) {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument')
                .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter')
                .where('instrument.symbol = :symbol', { symbol })
                .select('lotSizeFilter.qtyStep');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.lotSizeFilter_qtyStep ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getQtyStep',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getQtyStep(symbol);
        }
    }
    async getInstrument(param) {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument')
                .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
                .leftJoinAndSelect('instrument.priceFilter', 'priceFilter')
                .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter');
            if (param.symbol) {
                queryBuilder.andWhere('instrument.symbol = :symbol', {
                    symbol: param.symbol,
                });
            }
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'read',
                error: err,
                param,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getInstrument(param);
        }
    }
    async insert(param, enableListedTime) {
        try {
            const symbols = param.map((p) => p.symbol);
            const existingRecords = await this.InstrumentsRepository.find({
                where: { symbol: (0, typeorm_2.In)(symbols) },
            });
            const existingMap = new Map(existingRecords.map((r) => [r.symbol, r]));
            const mergedRecords = param.map((record) => {
                const existing = existingMap.get(record.symbol);
                if (existing) {
                    return {
                        ...existing,
                        ...record,
                        listedTime: enableListedTime
                            ? record.listedTime
                            : existing.listedTime,
                        auctionFeeInfo: {
                            ...(existing.auctionFeeInfo ?? {}),
                            ...(record.auctionFeeInfo ?? {}),
                        },
                        leverageFilter: {
                            ...(existing.leverageFilter ?? {}),
                            ...(record.leverageFilter ?? {}),
                        },
                        priceFilter: {
                            ...(existing.priceFilter ?? {}),
                            ...(record.priceFilter ?? {}),
                        },
                        lotSizeFilter: {
                            ...(existing.lotSizeFilter ?? {}),
                            ...(record.lotSizeFilter ?? {}),
                        },
                    };
                }
                else {
                    return record;
                }
            });
            return await this.InstrumentsRepository.save(mergedRecords);
        }
        catch (err) {
            this.logger.error('Failed to upsert instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'insert',
                error: err,
                param,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.insert(param, enableListedTime);
        }
    }
    async getTickSize(symbol) {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument')
                .leftJoinAndSelect('instrument.priceFilter', 'priceFilter')
                .where('instrument.symbol = :symbol', { symbol })
                .select('priceFilter.tickSize');
            const instrument = await queryBuilder.getRawOne();
            return instrument?.priceFilter_tickSize ?? 0;
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getTickSize',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getTickSize(symbol);
        }
    }
    async getSymbols() {
        try {
            const launchTimeThreshold = new Date().getTime() -
                (0, configurations_1.default)('SYMBOL_LAUNCH_TIME_THRESHOLD_DAYS') *
                    24 *
                    60 *
                    60 *
                    1000;
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument');
            queryBuilder.andWhere('instrument.launchTime < :launchTimeThreshold', {
                launchTimeThreshold,
            });
            const symbols = await queryBuilder.select('instrument.symbol').getMany();
            return symbols.map((s) => s.symbol);
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getSymbols',
                error: err,
            }));
            await (0, delay_util_1.delay)(5000);
            return await this.getSymbols();
        }
    }
};
exports.InstrumentService = InstrumentService;
exports.InstrumentService = InstrumentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(instrument_entity_1.InstrumentEntity)),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        winston_1.Logger])
], InstrumentService);
//# sourceMappingURL=instrument.service.js.map