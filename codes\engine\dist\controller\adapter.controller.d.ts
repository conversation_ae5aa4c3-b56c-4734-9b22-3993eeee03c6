import { Logger } from 'winston';
import { AdapterService } from 'src/services/adapter.service';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { SetLeverageDto } from 'src/dto/set-leverage.dto';
import { PlaceOrderDto } from 'src/dto/place-order.dto';
import { AmendOrderDto } from 'src/dto/amend-order.dto';
import { CancelOrderDto } from 'src/dto/cancel-order.dto';
import { GetActiveOrdersDto } from 'src/dto/get-active-orders.dto';
import { GetOrderHistoryDto } from 'src/dto/get-order-history.dto';
import { Historical } from 'src/interface/historical.interface';
import { Instrument } from 'src/interface/instrument.interface';
export declare class AdapterController {
    private readonly adapterService;
    private readonly logger;
    constructor(adapterService: AdapterService, logger: Logger);
    switchToHedgeMode(): Promise<any>;
    setLeverage(body: SetLeverageDto): Promise<any>;
    getWalletBalance(): Promise<number>;
    placeOrder(body: PlaceOrderDto): Promise<any>;
    amendOrder(body: AmendOrderDto): Promise<any>;
    cancelOrder(body: CancelOrderDto): Promise<any>;
    getActiveOrders(body: GetActiveOrdersDto): Promise<any[]>;
    getOrderHistory(body: GetOrderHistoryDto): Promise<any[]>;
    fetchBybitHistorical(body: GetHistoricalDto): Promise<Historical[]>;
    fetchBybitInstrument(body: GetInstrumentDto): Promise<Instrument[]>;
}
