export declare class AmendOrderDto {
    category: 'spot' | 'linear' | 'inverse' | 'option';
    symbol: string;
    orderId?: string;
    orderLinkId?: string;
    orderIv?: string;
    triggerPrice?: string;
    qty?: string;
    price?: string;
    takeProfit?: string;
    stopLoss?: string;
    tpTriggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';
    slTriggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';
    triggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';
    tpLimitPrice?: string;
    slLimitPrice?: string;
}
