export declare class MethodPerformanceEntity {
    methodId: string;
    fromDate: Date;
    endDate: Date;
    totalValidTrade: number;
    totalInvalidTrade: number;
    averageRewardRiskRatio: number;
    probability: number;
    avgOpenPosition: number;
    avgStopPercent: number;
    avgProfitPercent: number;
    avgConsecutiveLoss: number;
    avgConsecutiveProfit: number;
    avgHoldingPeriod: number;
    maxOpenPosition: number;
    maxStopPercent: number;
    maxProfitPercent: number;
    maxConsecutiveLoss: number;
    maxConsecutiveProfit: number;
    maxHoldingPeriod: number;
    cumulativePercentage: number;
    avgValidTradeByMonth: number;
    avgValidTradeByDay: number;
    avgRevenueByTrade: number;
}
