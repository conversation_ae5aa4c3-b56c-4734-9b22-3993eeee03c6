{"version": 3, "file": "adapter-external.service.js", "sourceRoot": "", "sources": ["../../src/services/adapter-external.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AACvD,qCAAiC;AACjC,kDAA0C;AAC1C,uEAAgD;AAChD,6DAAqD;AAWrD,mDAA4C;AAE5C,MAAM,oBAAoB,GAAG,IAAA,wBAAc,EAAC,sBAAsB,CAAC,CAAC;AACpE,MAAM,wBAAwB,GAAG,IAAA,wBAAc,EAAC,0BAA0B,CAAC,CAAC;AAE5E,MAAM,aAAa,GAAG,eAAK,CAAC,MAAM,CAAC;IACjC,OAAO,EAAE,oBAAoB;IAC7B,OAAO,EAAE;QACP,WAAW,EAAE,wBAAwB;KACtC;IACD,OAAO,EAAE,IAAI;CACd,CAAC,CAAC;AAGI,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAEmB;IADpD,YACoD,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAC/D,CAAC;IAEJ,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC;YACH,MAAM,aAAa,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAChD,QAAQ,EAAE,QAAQ;gBAClB,IAAI,EAAE,CAAC;aACR,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,EAChC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,mBAAmB;gBAC7B,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAA0B;QAC1C,IAAI,CAAC;YACH,MAAM,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,wBAAwB,EACxB,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,aAAa;gBACvB,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,iBAAiB,EAAE;gBAC1D,MAAM,EAAE,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE;aACjD,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,CAC1B,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,IAAI,CAAC,CAC1D,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,EAC9B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,kBAAkB;gBAC5B,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,KAAoB;QACnC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,IAAI,CAAC,SAAS,CAAC;gBACb,QAAQ,EAAE,YAAY;gBACtB,KAAK;gBACL,QAAQ,EAAE,QAAQ,CAAC,IAAI;aACxB,CAAC,CACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,EAC9B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,YAAY;gBACtB,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,KAAyB;QACxC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,IAAI,CAAC,SAAS,CAAC;gBACb,QAAQ,EAAE,YAAY;gBACtB,KAAK;gBACL,QAAQ,EAAE,QAAQ,CAAC,IAAI;aACxB,CAAC,CACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,EAC9B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,YAAY;gBACtB,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAA0B;QAC1C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAClE,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,IAAI,CAAC,SAAS,CAAC;gBACb,QAAQ,EAAE,aAAa;gBACvB,KAAK;gBACL,QAAQ,EAAE,QAAQ,CAAC,IAAI;aACxB,CAAC,CACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,+BAA+B,EAC/B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,aAAa;gBACvB,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAoB;QACxC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,gBAAgB,EAAE;gBACzD,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6BAA6B,EAC7B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,iBAAiB;gBAC3B,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAoB;QACxC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,gBAAgB,EAAE;gBACzD,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6BAA6B,EAC7B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,iBAAiB;gBAC3B,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,KAAuB;QAChD,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QAC7C,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAEzC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE;gBACtD,MAAM,EAAE;oBACN,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,KAAK,EAAE,cAAc;oBACrB,GAAG,EAAE,YAAY;oBACjB,KAAK,EAAE,KAAK,CAAC,KAAK;iBACnB;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,sDAAsD,EACtD,IAAA,2BAAS,EAAC;oBACR,KAAK,EAAE,YAAY;oBACnB,QAAQ,EAAE,sBAAsB;oBAChC,KAAK;iBACN,CAAC,CACH,CAAC;gBACF,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,MAAM,WAAW,GAAiB,OAAO;iBACtC,GAAG,CAAC,CAAC,IAAc,EAAE,EAAE,CAAC,CAAC;gBACxB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/B,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzB,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzB,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxB,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3B,QAAQ,EAAE,KAAK,CAAC,QAAQ;aACzB,CAAC,CAAC;iBACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACb,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBAC1B,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC7C,CAAC;YACH,CAAC,CAAC,CAAC;YAEL,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,KAAK,GAAG,GAAiB,CAAC;YAEhC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,EACjC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,sBAAsB;gBAChC,KAAK;gBACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK;aAC7C,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,KAAwB;QACjD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,mBAAmB,EAAE;gBAC5D,MAAM,EAAE;oBACN,QAAQ,EAAE,QAAQ;oBAClB,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;oBAClD,KAAK,EAAE,IAAA,wBAAc,EAAC,eAAe,CAAC;iBACvC;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,sDAAsD,EACtD,IAAA,2BAAS,EAAC;oBACR,KAAK,EAAE,YAAY;oBACnB,QAAQ,EAAE,sBAAsB;oBAChC,KAAK;iBACN,CAAC,CACH,CAAC;gBACF,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,MAAM,UAAU,GAAiB,OAAO,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC3D,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBACjC,UAAU,EAAE,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACvE,UAAU,EAAE,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACvE,UAAU,EAAE,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACvE,cAAc,EAAE;oBACd,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,IAAI,CAAC,CAAC;oBAC1D,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,IAAI,CAAC,CAAC;oBAC1D,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,IAAI,CAAC,CAAC;iBAC7D;gBACD,WAAW,EAAE;oBACX,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,IAAI,CAAC,CAAC;oBACjD,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,IAAI,CAAC,CAAC;oBACjD,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,IAAI,CAAC,CAAC;iBAClD;gBACD,aAAa,EAAE;oBACb,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,IAAI,CAAC,CAAC;oBACzD,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,IAAI,CAAC,CAAC;oBACzD,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC,CAAC;oBACjD,mBAAmB,EAAE,MAAM,CACzB,IAAI,CAAC,aAAa,EAAE,mBAAmB,IAAI,CAAC,CAC7C;oBACD,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,IAAI,CAAC,CAAC;oBAC/D,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,IAAI,CAAC,CAAC;iBACpE;gBACD,eAAe,EACb,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS;oBACjE,CAAC,CAAC,CAAC;oBACH,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;gBAClC,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;gBACpD,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;gBACpD,cAAc,EAAE;oBACd,cAAc,EAAE,IAAA,wBAAc,EAAC,wBAAwB,CAAC;oBACxD,YAAY,EAAE,IAAA,wBAAc,EAAC,sBAAsB,CAAC;oBACpD,YAAY,EAAE,IAAA,wBAAc,EAAC,sBAAsB,CAAC;iBACrD;aACF,CAAC,CAAC,CAAC;YAEJ,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,KAAK,GAAG,GAAiB,CAAC;YAEhC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,EACjC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,sBAAsB;gBAChC,KAAK;gBACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK;aAC7C,CAAC,CACH,CAAC;YACF,MAAM,IAAA,kBAAK,EAAC,IAAI,CAAC,CAAC;YAClB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;CACF,CAAA;AAtUY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;GAFvD,sBAAsB,CAsUlC"}