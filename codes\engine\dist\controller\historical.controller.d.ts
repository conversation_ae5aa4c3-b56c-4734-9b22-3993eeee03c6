import { Logger } from 'winston';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { Historical } from 'src/interface/historical.interface';
import { HistoricalCacheService } from 'src/services/historical-cache.service';
export declare class HistoricalController {
    private readonly historicalCacheService;
    private readonly logger;
    constructor(historicalCacheService: HistoricalCacheService, logger: Logger);
    get(body: GetHistoricalDto): Promise<Historical[]>;
    getAvailableMonths(symbol: string, interval: string): Promise<string[]>;
    getCacheInfo(symbol: string, interval: string): Promise<{
        totalMonths: number;
        months: string[];
        totalRecords: number;
        oldestRecord?: Date;
        newestRecord?: Date;
    }>;
    deleteMonthCache(symbol: string, interval: string, yearMonth: string): Promise<{
        message: string;
    }>;
    cleanupEmptyMonths(symbol: string, interval: string): Promise<{
        cleanedCount: number;
    }>;
}
