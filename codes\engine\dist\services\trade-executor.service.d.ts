import { Logger } from 'winston';
import { BacktestMultiMethodService } from './backtest-multi-method.service';
import { InstrumentService } from './instrument.service';
import { AdapterService } from './adapter.service';
import { TradeResultService } from './trade-result.service';
import { MethodService } from './method.service';
import { HistoricalCacheService } from './historical-cache.service';
export declare class TraderExecutorService {
    private readonly logger;
    private readonly backtestMultiMethodService;
    private readonly instrumentService;
    private readonly adapterService;
    private readonly tradeResultService;
    private readonly methodService;
    private readonly historicalCacheService;
    constructor(logger: Logger, backtestMultiMethodService: BacktestMultiMethodService, instrumentService: InstrumentService, adapterService: AdapterService, tradeResultService: TradeResultService, methodService: MethodService, historicalCacheService: HistoricalCacheService);
    run(): Promise<void>;
    private validatePendingResults;
    private cancel;
    private place;
    private orderPlan;
    private equityRatio;
    private calculateQty;
}
