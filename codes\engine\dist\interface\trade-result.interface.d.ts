import { OrderParamsV5 } from 'bybit-api';
export interface TradeResult extends OrderParamsV5 {
    id: string;
    methodId: string;
    date: Date;
    interval: string;
    stopPercent: number;
    profitPercent: number;
    expiryDate: Date;
    openDate?: Date;
    closedDate?: Date;
    status: 'pending' | 'expired' | 'open' | 'profit' | 'loss' | 'invalid' | 'canceled';
    action?: 'place' | 'amend';
    orderId?: string;
}
