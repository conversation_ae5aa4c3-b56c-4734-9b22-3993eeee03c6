"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyGuard = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const configurations_1 = __importDefault(require("../configurations"));
const log_detail_util_1 = require("../util/log-detail.util");
let ApiKeyGuard = class ApiKeyGuard {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const apiKey = this.extractApiKeyFromRequest(request);
        if (!apiKey) {
            this.logger.warn('API key missing from request', (0, log_detail_util_1.logDetail)({
                class: 'ApiKeyGuard',
                function: 'canActivate',
                param: {
                    userAgent: request.get('User-Agent'),
                    path: request.path,
                    ip: request.ip,
                },
            }));
            return false;
        }
        const validApiKey = (0, configurations_1.default)('ADAPTER_EXTERNAL_API_KEY');
        if (!validApiKey) {
            this.logger.error('ADAPTER_EXTERNAL_API_KEY not configured in environment', (0, log_detail_util_1.logDetail)({
                class: 'ApiKeyGuard',
                function: 'canActivate',
            }));
            return false;
        }
        if (apiKey !== validApiKey) {
            this.logger.warn('Invalid API key provided', (0, log_detail_util_1.logDetail)({
                class: 'ApiKeyGuard',
                function: 'canActivate',
                param: {
                    userAgent: request.get('User-Agent'),
                    path: request.path,
                    ip: request.ip,
                    providedApiKey: apiKey.substring(0, 8) + '***',
                },
            }));
            return false;
        }
        this.logger.debug('API key validation successful', (0, log_detail_util_1.logDetail)({
            class: 'ApiKeyGuard',
            function: 'canActivate',
            param: { ip: request.ip, path: request.path },
        }));
        return true;
    }
    extractApiKeyFromRequest(request) {
        const authHeader = request.get('Authorization');
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        return undefined;
    }
};
exports.ApiKeyGuard = ApiKeyGuard;
exports.ApiKeyGuard = ApiKeyGuard = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger])
], ApiKeyGuard);
//# sourceMappingURL=api-key.guard.js.map