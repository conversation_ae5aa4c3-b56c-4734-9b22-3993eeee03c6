"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdapterController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const adapter_service_1 = require("../services/adapter.service");
const get_historical_dto_1 = require("../dto/get-historical.dto");
const get_instrument_dto_1 = require("../dto/get-instrument.dto");
const set_leverage_dto_1 = require("../dto/set-leverage.dto");
const place_order_dto_1 = require("../dto/place-order.dto");
const amend_order_dto_1 = require("../dto/amend-order.dto");
const cancel_order_dto_1 = require("../dto/cancel-order.dto");
const get_active_orders_dto_1 = require("../dto/get-active-orders.dto");
const get_order_history_dto_1 = require("../dto/get-order-history.dto");
const log_detail_util_1 = require("../util/log-detail.util");
const api_key_decorator_1 = require("../decorators/api-key.decorator");
let AdapterController = class AdapterController {
    adapterService;
    logger;
    constructor(adapterService, logger) {
        this.adapterService = adapterService;
        this.logger = logger;
    }
    async switchToHedgeMode() {
        try {
            await this.adapterService.switchToHedgeMode();
            return { message: 'Switched to hedge mode' };
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Failed to switch to hedge mode', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'switchToHedgeMode',
                error: error.stack || message,
            }));
            return { error: message, success: false };
        }
    }
    async setLeverage(body) {
        try {
            await this.adapterService.setLeverage(body);
            return { message: 'Leverage set' };
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Failed to set leverage', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'setLeverage',
                body,
                error: error.stack || message,
            }));
            return { error: message, success: false };
        }
    }
    async getWalletBalance() {
        try {
            const result = await this.adapterService.getWalletBalance();
            return result ?? 0;
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Failed to get wallet balance', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'getWalletBalance',
                error: error.stack || message,
            }));
            return 0;
        }
    }
    async placeOrder(body) {
        try {
            await this.adapterService.placeOrder(body);
            return { message: 'Order placed' };
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Failed to place order', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'placeOrder',
                body,
                error: error.stack || message,
            }));
            return { error: message, success: false };
        }
    }
    async amendOrder(body) {
        try {
            await this.adapterService.amendOrder(body);
            return { message: 'Order amended' };
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Failed to amend order', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'amendOrder',
                body,
                error: error.stack || message,
            }));
            return { error: message, success: false };
        }
    }
    async cancelOrder(body) {
        try {
            await this.adapterService.cancelOrder(body);
            return { message: 'Order canceled' };
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Failed to cancel order', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'cancelOrder',
                body,
                error: error.stack || message,
            }));
            return { error: message, success: false };
        }
    }
    async getActiveOrders(body) {
        try {
            const result = await this.adapterService.getActiveOrders(body);
            return result ?? [];
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Failed to get active orders', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'getActiveOrders',
                body,
                error: error.stack || message,
            }));
            return [];
        }
    }
    async getOrderHistory(body) {
        try {
            const result = await this.adapterService.getOrderHistory(body);
            return result ?? [];
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Failed to get order history', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'getOrderHistory',
                body,
                error: error.stack || message,
            }));
            return [];
        }
    }
    async fetchBybitHistorical(body) {
        try {
            body.start = new Date(body.start);
            body.end = new Date(body.end);
            const result = await this.adapterService.fetchBybitHistorical(body);
            return result ?? [];
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Historical data fetch failed', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'fetchBybitHistorical',
                body,
                error: error.stack || message,
            }));
            return [];
        }
    }
    async fetchBybitInstrument(body) {
        try {
            const result = await this.adapterService.fetchBybitInstrument(body);
            return result ?? [];
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Instrument data fetch failed', (0, log_detail_util_1.logDetail)({
                class: 'AdapterController',
                function: 'fetchBybitInstrument',
                body,
                error: error.stack || message,
            }));
            return [];
        }
    }
};
exports.AdapterController = AdapterController;
__decorate([
    (0, common_1.Post)('/switch-to-hedge-mode'),
    (0, swagger_1.ApiOperation)({ summary: 'Switch to Hedge Mode' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Switched to hedge mode' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "switchToHedgeMode", null);
__decorate([
    (0, common_1.Post)('/set-leverage'),
    (0, swagger_1.ApiOperation)({ summary: 'Set Leverage' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Leverage set' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [set_leverage_dto_1.SetLeverageDto]),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "setLeverage", null);
__decorate([
    (0, common_1.Post)('/get-wallet-balance'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Wallet Balance' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Wallet Balance' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "getWalletBalance", null);
__decorate([
    (0, common_1.Post)('/place-order'),
    (0, swagger_1.ApiOperation)({ summary: 'Place Order' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Order placed' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [place_order_dto_1.PlaceOrderDto]),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "placeOrder", null);
__decorate([
    (0, common_1.Post)('/amend-order'),
    (0, swagger_1.ApiOperation)({ summary: 'Amend Order' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Order amended' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [amend_order_dto_1.AmendOrderDto]),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "amendOrder", null);
__decorate([
    (0, common_1.Post)('/cancel-order'),
    (0, swagger_1.ApiOperation)({ summary: 'Cancel Order' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Order canceled' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [cancel_order_dto_1.CancelOrderDto]),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "cancelOrder", null);
__decorate([
    (0, common_1.Post)('/active-orders'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Active Orders' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of active orders',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_active_orders_dto_1.GetActiveOrdersDto]),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "getActiveOrders", null);
__decorate([
    (0, common_1.Post)('/order-history'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Order History' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of order history',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_order_history_dto_1.GetOrderHistoryDto]),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "getOrderHistory", null);
__decorate([
    (0, common_1.Post)('/historical'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Historical Data' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Historical Data',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_historical_dto_1.GetHistoricalDto]),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "fetchBybitHistorical", null);
__decorate([
    (0, common_1.Post)('/instrument'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Instrument Data' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Instrument Data',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_instrument_dto_1.GetInstrumentDto]),
    __metadata("design:returntype", Promise)
], AdapterController.prototype, "fetchBybitInstrument", null);
exports.AdapterController = AdapterController = __decorate([
    (0, swagger_1.ApiTags)('Adapter API'),
    (0, api_key_decorator_1.ApiKeyAuth)(),
    (0, common_1.Controller)('adapter'),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [adapter_service_1.AdapterService,
        winston_1.Logger])
], AdapterController);
//# sourceMappingURL=adapter.controller.js.map