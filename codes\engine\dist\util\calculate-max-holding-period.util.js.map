{"version": 3, "file": "calculate-max-holding-period.util.js", "sourceRoot": "", "sources": ["../../src/util/calculate-max-holding-period.util.ts"], "names": [], "mappings": ";;AAEA,8DAoBC;AApBD,SAAgB,yBAAyB,CAAC,KAAqB;IAC7D,IAAI,cAAc,GAAG,CAAC,CAAC;IAEvB,KAAK,MAAM,MAAM,IAAI,KAAK,EAAE,CAAC;QAC3B,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;QAGhD,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,MAAM,CAAC,IAAI,QAAQ,IAAI,UAAU,EAAE,CAAC;YACzE,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;YACnC,MAAM,WAAW,GACf,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAE7D,IAAI,WAAW,GAAG,cAAc,EAAE,CAAC;gBACjC,cAAc,GAAG,WAAW,CAAC;YAC/B,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AACnC,CAAC"}