{"version": 3, "file": "backtest-multi-method.service.js", "sourceRoot": "", "sources": ["../../src/services/backtest-multi-method.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AACvD,qCAAiC;AACjC,yDAAqD;AACrD,6DAAqD;AAIrD,qDAAiD;AAG1C,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IAEe;IACjC;IACA;IAHnB,YACoD,MAAc,EAC/C,eAAgC,EAChC,aAA4B;QAFK,WAAM,GAAN,MAAM,CAAQ;QAC/C,oBAAe,GAAf,eAAe,CAAiB;QAChC,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAEJ,KAAK,CAAC,4BAA4B,CAChC,KAAgC;QAEhC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAErE,OAAO,KAAK,CAAC,iBAAiB;gBAC5B,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;gBACrD,CAAC,CAAC,OAAO,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,EAC3C,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,yBAAyB;gBACnC,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iCAAiC,CACrC,KAAgC;QAEhC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACrE,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;iBACnE,MAAM,CAAC;YACV,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvE,OAAO;gBACL,WAAW;gBACX,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,eAAe,EAAE,WAAW,CAAC,eAAe;gBAC5C,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;gBAChD,sBAAsB,EAAE,WAAW,CAAC,sBAAsB;gBAC1D,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,eAAe,EAAE,WAAW,CAAC,eAAe;gBAC5C,cAAc,EAAE,WAAW,CAAC,cAAc;gBAC1C,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;gBAC9C,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;gBAClD,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;gBACtD,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;gBAC9C,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;gBACtD,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;gBAChD,eAAe,EAAE,WAAW,CAAC,eAAe;gBAC5C,cAAc,EAAE,WAAW,CAAC,cAAc;gBAC1C,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;gBAC9C,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;gBAClD,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;gBACtD,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;gBAC9C,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;gBACtD,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;aACnD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gDAAgD,EAChD,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,8BAA8B;gBACxC,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO;gBACL,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;gBACrB,OAAO,EAAE,IAAI,IAAI,EAAE;gBACnB,eAAe,EAAE,CAAC;gBAClB,iBAAiB,EAAE,CAAC;gBACpB,sBAAsB,EAAE,CAAC;gBACzB,WAAW,EAAE,CAAC;gBACd,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,CAAC;gBACjB,gBAAgB,EAAE,CAAC;gBACnB,kBAAkB,EAAE,CAAC;gBACrB,oBAAoB,EAAE,CAAC;gBACvB,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,CAAC;gBACjB,gBAAgB,EAAE,CAAC;gBACnB,kBAAkB,EAAE,CAAC;gBACrB,oBAAoB,EAAE,CAAC;gBACvB,gBAAgB,EAAE,CAAC;gBACnB,oBAAoB,EAAE,CAAC;gBACvB,oBAAoB,EAAE,CAAC;gBACvB,kBAAkB,EAAE,CAAC;gBACrB,iBAAiB,EAAE,CAAC;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,KAAgC;QAI/D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACrE,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;iBACnE,MAAM,CAAC;YACV,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvE,OAAO;gBACL,MAAM,EAAE,KAAK,CAAC,iBAAiB;oBAC7B,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;oBACrD,CAAC,CAAC,OAAO;gBACX,WAAW,EAAE;oBACX,WAAW;oBACX,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,eAAe,EAAE,WAAW,CAAC,eAAe;oBAC5C,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;oBAChD,sBAAsB,EAAE,WAAW,CAAC,sBAAsB;oBAC1D,WAAW,EAAE,WAAW,CAAC,WAAW;oBACpC,eAAe,EAAE,WAAW,CAAC,eAAe;oBAC5C,cAAc,EAAE,WAAW,CAAC,cAAc;oBAC1C,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;oBAC9C,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;oBAClD,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;oBACtD,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;oBAC9C,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;oBACtD,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;oBAChD,eAAe,EAAE,WAAW,CAAC,eAAe;oBAC5C,cAAc,EAAE,WAAW,CAAC,cAAc;oBAC1C,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;oBAC9C,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;oBAClD,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;oBACtD,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;oBAC9C,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;oBACtD,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;iBACnD;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yCAAyC,EACzC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,uBAAuB;gBACjC,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,OAAO;gBACL,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE;oBACX,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;oBACrB,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,eAAe,EAAE,CAAC;oBAClB,iBAAiB,EAAE,CAAC;oBACpB,sBAAsB,EAAE,CAAC;oBACzB,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,CAAC;oBACjB,gBAAgB,EAAE,CAAC;oBACnB,kBAAkB,EAAE,CAAC;oBACrB,oBAAoB,EAAE,CAAC;oBACvB,gBAAgB,EAAE,CAAC;oBACnB,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,CAAC;oBACjB,gBAAgB,EAAE,CAAC;oBACnB,kBAAkB,EAAE,CAAC;oBACrB,oBAAoB,EAAE,CAAC;oBACvB,gBAAgB,EAAE,CAAC;oBACnB,oBAAoB,EAAE,CAAC;oBACvB,oBAAoB,EAAE,CAAC;oBACvB,kBAAkB,EAAE,CAAC;oBACrB,iBAAiB,EAAE,CAAC;iBACrB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AApLY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;QAC9B,kCAAe;QACjB,8BAAa;GAJpC,0BAA0B,CAoLtC"}